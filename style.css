/* CSS 自定义属性 - 颜色系统 */
:root {
  /* 主色调 */
  --color-primary: #3B82F6;
  --color-primary-hover: #2563EB;
  --color-primary-light: #DBEAFE;
  
  /* 辅助色 */
  --color-secondary: #6B7280;
  --color-secondary-hover: #4B5563;
  --color-secondary-light: #F3F4F6;
  
  /* 状态色 */
  --color-success: #10B981;
  --color-success-light: #D1FAE5;
  --color-warning: #F59E0B;
  --color-warning-light: #FEF3C7;
  --color-error: #EF4444;
  --color-error-light: #FEE2E2;
  --color-info: #06B6D4;
  --color-info-light: #CFFAFE;
  
  /* 中性色 */
  --color-white: #FFFFFF;
  --color-gray-50: #F9FAFB;
  --color-gray-100: #F3F4F6;
  --color-gray-200: #E5E7EB;
  --color-gray-300: #D1D5DB;
  --color-gray-400: #9CA3AF;
  --color-gray-500: #6B7280;
  --color-gray-600: #4B5563;
  --color-gray-700: #374151;
  --color-gray-800: #1F2937;
  --color-gray-900: #111827;
  
  /* 背景色 */
  --bg-primary: var(--color-white);
  --bg-secondary: var(--color-gray-50);
  --bg-tertiary: var(--color-gray-100);
  
  /* 文字色 */
  --text-primary: var(--color-gray-900);
  --text-secondary: var(--color-gray-600);
  --text-tertiary: var(--color-gray-400);
  
  /* 边框色 */
  --border-color: var(--color-gray-200);
  --border-hover: var(--color-gray-300);
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* 紧凑化间距系统 - 大幅减少所有间距 */
  --spacing-1: 0.125rem;  /* 2px - 原0.25rem */
  --spacing-2: 0.25rem;   /* 4px - 原0.5rem */
  --spacing-3: 0.375rem;  /* 6px - 原0.75rem */
  --spacing-4: 0.5rem;    /* 8px - 原1rem */
  --spacing-5: 0.625rem;  /* 10px - 原1.25rem */
  --spacing-6: 0.75rem;   /* 12px - 原1.5rem */
  --spacing-8: 1rem;      /* 16px - 原2rem */
  --spacing-10: 1.25rem;  /* 20px - 原2.5rem */
  --spacing-12: 1.5rem;   /* 24px - 原3rem */
  
  /* 圆角 */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  
  /* 过渡动画 */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* 字体 */
  --font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  
  /* 行高 */
  --line-height-tight: 1.2;   /* 更紧凑的行高 */
  --line-height-normal: 1.4;  /* 减少行高 */
  --line-height-relaxed: 1.6; /* 减少行高 */
}

/* 暗色主题 */
[data-theme="dark"] {
  --bg-primary: var(--color-gray-900);
  --bg-secondary: var(--color-gray-800);
  --bg-tertiary: var(--color-gray-700);
  --text-primary: var(--color-gray-100);
  --text-secondary: var(--color-gray-300);
  --text-tertiary: var(--color-gray-500);
  --border-color: var(--color-gray-700);
  --border-hover: var(--color-gray-600);
}

/* 基础重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 布局容器 */
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-3) 0; /* 减少头部padding */
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-title {
  font-size: var(--font-size-xl);  /* 减少标题大小 */
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.title-icon {
  font-size: var(--font-size-2xl);  /* 减少图标大小 */
}

.header-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);  /* 减少控件间距 */
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);  /* 减少用户信息间距 */
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.main-content {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-4) var(--spacing-4); /* 大幅减少主内容padding */
  width: 100%;
}

/* 登录面板 */
.login-panel {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.login-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-6); /* 减少登录卡片padding */
  width: 100%;
  max-width: 400px;
  border: 1px solid var(--border-color);
}

.login-card h2 {
  text-align: center;
  margin-bottom: var(--spacing-4); /* 减少标题下方间距 */
  color: var(--text-primary);
  font-size: var(--font-size-xl);
}

.login-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.login-actions .btn {
  width: 100%;
}

.login-actions .btn-sm {
  font-size: var(--font-size-sm);
  padding: var(--spacing-2) var(--spacing-3);
}

/* 工作区 */
.workspace {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4); /* 大幅减少工作区间距 */
}

/* 区域样式 */
.input-section,
.preview-section,
.console-section {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.section-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-3); /* 减少区域头部padding */
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h3 {
  font-size: var(--font-size-base); /* 减少区域标题大小 */
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.section-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.input-card,
.preview-card,
.console-card {
  padding: var(--spacing-4); /* 大幅减少卡片内部padding */
}

/* 实时分析相关样式 */
.realtime-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.realtime-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  background: var(--color-primary-light);
  color: var(--color-primary);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
  width: fit-content;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: var(--spacing-3); /* 减少输入操作间距 */
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--border-color);
}

.gemini-status {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-weight: 500;
}

/* 表单样式 - 大幅紧凑化 */
.form-group {
  margin-bottom: var(--spacing-2); /* 从spacing-4减少到spacing-2 */
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-1); /* 减少标签下方间距 */
  font-weight: 500;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-2); /* 从spacing-3减少到spacing-2 */
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm); /* 减少字体大小 */
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-light); /* 减少焦点阴影 */
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: var(--color-error);
  box-shadow: 0 0 0 2px var(--color-error-light);
}

.field-error {
  color: var(--color-error);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px; /* 减少文本域最小高度 */
}

.form-section {
  margin-bottom: var(--spacing-4); /* 从spacing-8大幅减少到spacing-4 */
  padding-bottom: var(--spacing-3); /* 从spacing-6减少到spacing-3 */
  border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section h4 {
  font-size: var(--font-size-base); /* 减少表单区域标题大小 */
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-3); /* 减少标题下方间距 */
  padding-bottom: var(--spacing-1);
  border-bottom: 2px solid var(--color-primary);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); /* 减少最小列宽 */
  gap: var(--spacing-3); /* 减少网格间距 */
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  margin-bottom: 0;
}

.checkbox-group input[type="checkbox"] {
  width: auto;
  margin: 0;
}

/* 复选框样式 */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  margin-bottom: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  user-select: none;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
  cursor: pointer;
}

.checkbox-text {
  cursor: pointer;
}

.checkbox-label:hover {
  color: var(--text-primary);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3); /* 减少按钮padding */
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--color-primary);
  color: var(--color-white);
  border-color: var(--color-primary);
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--color-secondary);
  color: var(--color-white);
  border-color: var(--color-secondary);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-secondary-hover);
  border-color: var(--color-secondary-hover);
}

.btn-outline {
  background: transparent;
  color: var(--text-primary);
  border-color: var(--border-color);
}

.btn-outline:hover:not(:disabled) {
  background: var(--bg-tertiary);
  border-color: var(--border-hover);
}

.btn-sm {
  padding: var(--spacing-1) var(--spacing-2); /* 减少小按钮padding */
  font-size: var(--font-size-xs);
}

.btn-icon {
  width: 32px;  /* 减少图标按钮大小 */
  height: 32px;
  padding: 0;
  border-radius: 50%;
}

.loading-spinner {
  font-size: var(--font-size-base);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 操作按钮组 */
.form-actions {
  display: flex;
  gap: var(--spacing-2); /* 减少按钮组间距 */
  margin-top: var(--spacing-4); /* 减少顶部间距 */
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--border-color);
}

/* 日志控制台 */
.log-console {
  background: var(--color-gray-900);
  color: var(--color-gray-100);
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  padding: var(--spacing-3); /* 减少控制台padding */
  border-radius: var(--radius-md);
  height: 250px; /* 减少控制台高度 */
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.log-entry {
  margin-bottom: var(--spacing-1); /* 减少日志条目间距 */
  padding: var(--spacing-1);
  border-radius: var(--radius-sm);
  border-left: 3px solid transparent;
}

.log-entry.info {
  background: rgba(6, 182, 212, 0.1);
  border-left-color: var(--color-info);
}

.log-entry.success {
  background: rgba(16, 185, 129, 0.1);
  border-left-color: var(--color-success);
}

.log-entry.warning {
  background: rgba(245, 158, 11, 0.1);
  border-left-color: var(--color-warning);
}

.log-entry.error {
  background: rgba(239, 68, 68, 0.1);
  border-left-color: var(--color-error);
}

.log-timestamp {
  color: var(--color-gray-400);
  font-size: var(--font-size-xs);
}

/* 切换开关 */
.toggle-switch {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.toggle-switch input {
  display: none;
}

.toggle-slider {
  width: 40px; /* 减少开关大小 */
  height: 20px;
  background: var(--color-gray-300);
  border-radius: 10px;
  position: relative;
  transition: background-color var(--transition-fast);
}

.toggle-slider::before {
  content: '';
  position: absolute;
  width: 16px; /* 减少开关按钮大小 */
  height: 16px;
  border-radius: 50%;
  background: var(--color-white);
  top: 2px;
  left: 2px;
  transition: transform var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.toggle-switch input:checked + .toggle-slider {
  background: var(--color-primary);
}

.toggle-switch input:checked + .toggle-slider::before {
  transform: translateX(20px);
}

/* 状态栏 */
.status-bar {
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-1) var(--spacing-4); /* 减少状态栏padding */
  font-size: var(--font-size-xs);
}

.status-info {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  gap: var(--spacing-3);
}

.status-item {
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

/* 模态框 - 重点优化，确保订单预览无滚动显示 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  padding: var(--spacing-4); /* 添加模态框外边距 */
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 900px; /* 增加最大宽度 */
  max-height: 90vh; /* 减少最大高度，确保有边距 */
  overflow: hidden;
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
}

.modal-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-3); /* 减少模态框头部padding */
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.modal-header h3 {
  font-size: var(--font-size-base); /* 减少模态框标题大小 */
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-3); /* 大幅减少模态框body padding */
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

.modal-footer {
  background: var(--bg-tertiary);
  padding: var(--spacing-3); /* 减少模态框footer padding */
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-2); /* 减少按钮间距 */
  flex-shrink: 0;
}

/* 订单预览专用样式 - 极致紧凑化 */
.order-preview {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-tight);
}

.order-preview h4 {
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-2);
  color: var(--color-primary);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--spacing-1);
}

.order-preview h5 {
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-2);
  color: var(--text-primary);
  font-weight: 600;
}

.order-preview p {
  margin: 0 0 var(--spacing-1) 0; /* 极致减少段落间距 */
  line-height: var(--line-height-tight);
}

.preview-section {
  margin-bottom: var(--spacing-3); /* 减少预览区域间距 */
  padding-bottom: var(--spacing-2);
  border-bottom: 1px solid var(--border-color);
}

.preview-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

/* 订单预览多列布局 - 关键优化 */
.preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-4);
}

.preview-grid .preview-section {
  border-bottom: none;
  background: var(--bg-secondary);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

/* 订单预览内容优化 */
.preview-section p strong {
  display: inline-block;
  min-width: 80px; /* 固定标签宽度，对齐内容 */
  color: var(--text-secondary);
  font-weight: 500;
}

/* API结果显示 */
.api-result-block {
  margin-top: var(--spacing-3);
}

.api-result-json {
  background: #222 !important;
  color: #fff !important;
  padding: var(--spacing-2) !important;
  border-radius: var(--radius-md) !important;
  max-height: 200px !important; /* 限制高度 */
  overflow: auto !important;
  font-size: var(--font-size-xs) !important;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
}

/* 成功/错误消息样式 */
.success-message,
.error-message {
  margin-bottom: var(--spacing-3);
}

.success-message p,
.error-message p {
  margin-bottom: var(--spacing-1);
}

/* 订单成功样式 */
.order-success-header {
  text-align: center;
  margin-bottom: var(--spacing-3);
  padding-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
}

.order-success-header h4 {
  color: var(--color-success);
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-lg);
}

.order-id-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  margin-top: var(--spacing-2);
}

.order-id-highlight {
  color: var(--color-primary);
  font-size: var(--font-size-lg);
  font-family: 'Courier New', monospace;
  background: var(--color-primary-light);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-primary);
}

.order-details {
  background: var(--bg-secondary);
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  margin-top: var(--spacing-3);
}

.order-details p {
  margin: var(--spacing-1) 0;
  font-size: var(--font-size-sm);
}

/* 响应式设计 - 大幅优化 */
@media (max-width: 1024px) {
  .main-content {
    max-width: 100vw;
    padding: var(--spacing-3) var(--spacing-2);
  }
  
  .input-card,
  .preview-card {
    padding: var(--spacing-3);
  }
  
  .form-section {
    margin-bottom: var(--spacing-3);
    padding-bottom: var(--spacing-2);
  }
  
  .form-group {
    margin-bottom: var(--spacing-2);
  }
  
  .form-actions,
  .input-actions {
    margin-top: var(--spacing-3);
    padding-top: var(--spacing-2);
    gap: var(--spacing-2);
  }
  
  .workspace {
    gap: var(--spacing-3);
  }

  .modal-content {
    max-width: 95vw;
  }

  .preview-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-3);
  }
}

@media (max-width: 768px) {
  html {
    font-size: 15px;
  }
  
  .header-content {
    flex-direction: column;
    gap: var(--spacing-2);
    padding: 0 var(--spacing-2);
  }
  
  .app-title {
    font-size: var(--font-size-lg);
  }
  
  .main-content {
    padding: var(--spacing-2) var(--spacing-1);
  }
  
  .form-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-2);
  }
  
  .section-header {
    flex-direction: column;
    gap: var(--spacing-2);
    align-items: flex-start;
    padding: var(--spacing-2);
  }
  
  .section-controls {
    width: 100%;
    justify-content: flex-end;
  }
  
  .form-actions,
  .input-actions {
    flex-direction: column;
    gap: var(--spacing-2);
  }
  
  .input-actions {
    align-items: stretch;
  }
  
  .login-card {
    margin: var(--spacing-2);
    padding: var(--spacing-4);
  }
  
  .modal {
    padding: var(--spacing-2);
  }

  .modal-content {
    width: 100%;
    max-width: 100vw;
    max-height: 95vh;
  }
  
  .modal-body {
    padding: var(--spacing-2);
  }

  .modal-header,
  .modal-footer {
    padding: var(--spacing-2);
  }

  /* 手机端订单预览单列布局 */
  .preview-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-2);
  }

  .preview-grid .preview-section {
    padding: var(--spacing-2);
  }

  .preview-section p strong {
    min-width: 70px;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 14px;
  }
  
  .main-content {
    padding: var(--spacing-1);
  }
  
  .input-card,
  .preview-card {
    padding: var(--spacing-2);
  }
  
  .form-section {
    margin-bottom: var(--spacing-2);
    padding-bottom: var(--spacing-1);
  }
  
  .form-group {
    margin-bottom: var(--spacing-1);
  }
  
  .form-actions,
  .input-actions {
    margin-top: var(--spacing-2);
    padding-top: var(--spacing-1);
    gap: var(--spacing-1);
  }
  
  .workspace {
    gap: var(--spacing-2);
  }

  .modal {
    padding: var(--spacing-1);
  }
  
  .modal-content {
    width: 100%;
    max-width: 100vw;
    max-height: 98vh;
    border-radius: var(--radius-md);
  }
  
  .modal-body {
    padding: var(--spacing-2);
  }

  .modal-header,
  .modal-footer {
    padding: var(--spacing-2);
  }

  .section-header {
    padding: var(--spacing-2);
  }

  .preview-section p strong {
    min-width: 60px;
    font-size: var(--font-size-xs);
  }

  .api-result-json {
    max-height: 150px !important;
  }

  /* 极小屏幕的特殊优化 */
  .form-section h4 {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-2);
  }

  .order-preview h5 {
    font-size: var(--font-size-xs);
    margin-bottom: var(--spacing-1);
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px); /* 减少动画距离 */
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.fade-in {
  animation: fadeIn var(--transition-normal) ease-out;
}

.slide-in {
  animation: slideIn var(--transition-normal) ease-out;
}

/* 实用类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.visible { display: block !important; }

.mt-4 { margin-top: var(--spacing-4); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mr-4 { margin-right: var(--spacing-4); }
.ml-4 { margin-left: var(--spacing-4); }

.p-4 { padding: var(--spacing-4); }
.pt-4 { padding-top: var(--spacing-4); }
.pb-4 { padding-bottom: var(--spacing-4); }
.pr-4 { padding-right: var(--spacing-4); }
.pl-4 { padding-left: var(--spacing-4); }

/* 预览卡片额外优化 */
.preview-card {
  padding: var(--spacing-3);
}

/* 紧凑模式的额外CSS变量 */
:root {
  --compact-spacing: var(--spacing-1);
  --compact-gap: var(--spacing-2);
}

/* ========================================
   订单预览浮窗样式 - UI重构新增
   ======================================== */

/* 预览区域默认隐藏 */
.preview-section {
  display: none !important;
}

/* 当显示预览时的样式 */
.preview-section.show-preview {
  display: block !important;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
}

/* 浮窗遮罩层 */
.preview-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2001;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  padding: var(--spacing-4);
}

/* 浮窗内容容器 */
.preview-modal-content {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 1000px;
  max-height: 90vh;
  overflow: hidden;
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  animation: modalSlideIn 0.3s ease-out;
}

/* 浮窗头部 */
.preview-modal-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.preview-modal-header h3 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin: 0;
}

.preview-modal-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* 浮窗主体 */
.preview-modal-body {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

/* 浮窗动画 */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* ========================================
   可编辑字段样式
   ======================================== */

/* 字段容器 */
.field-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* 编辑按钮 */
.edit-field-btn {
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-1);
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  flex-shrink: 0;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
}

.edit-field-btn:hover {
  background: var(--bg-tertiary);
  border-color: var(--color-primary);
  color: var(--color-primary);
  opacity: 1;
  transform: scale(1.05);
}

.edit-field-btn:active {
  transform: scale(0.95);
}

/* 字段编辑状态 */
.editable-field.editing .edit-field-btn {
  background: var(--color-primary-light);
  border-color: var(--color-primary);
  color: var(--color-primary);
  opacity: 1;
}

.editable-field.editing input,
.editable-field.editing select,
.editable-field.editing textarea {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-light);
  background: var(--color-primary-light);
}

/* 字段保存和取消按钮 */
.field-actions {
  display: none;
  gap: var(--spacing-1);
  margin-top: var(--spacing-1);
}

.editable-field.editing .field-actions {
  display: flex;
}

.field-action-btn {
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-1) var(--spacing-2);
  cursor: pointer;
  font-size: var(--font-size-xs);
  transition: all var(--transition-fast);
}

.field-action-btn.save {
  color: var(--color-success);
  border-color: var(--color-success);
}

.field-action-btn.save:hover {
  background: var(--color-success);
  color: white;
}

.field-action-btn.cancel {
  color: var(--color-error);
  border-color: var(--color-error);
}

.field-action-btn.cancel:hover {
  background: var(--color-error);
  color: white;
}

/* ========================================
   预览浮窗响应式设计
   ======================================== */

/* 平板设备 */
@media (max-width: 1024px) {
  .preview-modal-overlay {
    padding: var(--spacing-3);
  }

  .preview-modal-content {
    max-width: 95vw;
  }

  .preview-modal-header {
    padding: var(--spacing-3);
    flex-direction: column;
    gap: var(--spacing-2);
    align-items: flex-start;
  }

  .preview-modal-controls {
    width: 100%;
    justify-content: flex-end;
  }
}

/* 手机设备 */
@media (max-width: 768px) {
  .preview-modal-overlay {
    padding: var(--spacing-2);
  }

  .preview-modal-content {
    max-width: 100vw;
    max-height: 95vh;
    border-radius: var(--radius-md);
  }

  .preview-modal-header {
    padding: var(--spacing-2);
  }

  .preview-modal-header h3 {
    font-size: var(--font-size-base);
  }

  .edit-field-btn {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }

  .field-container {
    gap: var(--spacing-1);
  }
}

/* 小屏手机 */
@media (max-width: 480px) {
  .preview-modal-overlay {
    padding: var(--spacing-1);
  }

  .preview-modal-content {
    max-height: 98vh;
  }

  .preview-modal-header {
    padding: var(--spacing-2);
  }

  .edit-field-btn {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }

  .field-action-btn {
    padding: 2px var(--spacing-1);
    font-size: 10px;
  }
}