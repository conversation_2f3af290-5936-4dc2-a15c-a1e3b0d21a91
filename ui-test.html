<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI重构测试页面</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="app">
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <span class="title-icon">🚗</span>
                    OTA订单处理系统 - UI重构测试
                </h1>
            </div>
        </header>

        <main class="main-content">
            <div class="workspace">
                <!-- 智能输入区 -->
                <section class="input-section">
                    <div class="section-header">
                        <h3>🤖 智能订单解析测试</h3>
                        <div class="section-controls">
                            <button id="testShowModal" class="btn btn-primary">显示预览浮窗</button>
                            <button id="testHideModal" class="btn btn-outline">隐藏预览浮窗</button>
                        </div>
                    </div>
                    <div class="input-card">
                        <p>点击上方按钮测试预览浮窗的显示和隐藏功能。</p>
                        <p>预览浮窗包含完整的订单表单，每个字段都有编辑图标。</p>
                    </div>
                </section>

                <!-- 订单预览区 - 重构为浮窗模式 -->
                <section class="preview-section" id="previewSection" style="display: none;">
                    <!-- 浮窗遮罩 -->
                    <div class="preview-modal-overlay" id="previewModalOverlay">
                        <div class="preview-modal-content">
                            <div class="preview-modal-header">
                                <h3>📋 订单预览与编辑</h3>
                                <div class="preview-modal-controls">
                                    <button id="validateOrder" class="btn btn-outline btn-sm">验证数据</button>
                                    <button id="resetOrder" class="btn btn-outline btn-sm">重置</button>
                                    <button id="closePreviewModal" class="btn btn-icon" title="关闭">✕</button>
                                </div>
                            </div>
                            <div class="preview-modal-body">
                                <div class="preview-card">
                                    <form id="orderForm" class="order-form">
                                        <!-- 基本信息 -->
                                        <div class="form-section">
                                            <h4>基本信息</h4>
                                            <div class="form-grid">
                                                <div class="form-group editable-field">
                                                    <label for="subCategoryId">子分类 *</label>
                                                    <div class="field-container">
                                                        <select id="subCategoryId" required>
                                                            <option value="">请选择子分类</option>
                                                            <option value="2">接机服务</option>
                                                            <option value="3">送机服务</option>
                                                            <option value="4">包车服务</option>
                                                        </select>
                                                        <button type="button" class="edit-field-btn" data-field="subCategoryId" title="编辑">✏️</button>
                                                    </div>
                                                </div>
                                                <div class="form-group editable-field">
                                                    <label for="otaReferenceNumber">OTA参考号 *</label>
                                                    <div class="field-container">
                                                        <input type="text" id="otaReferenceNumber" placeholder="GMH-" required value="GMH-TEST-001">
                                                        <button type="button" class="edit-field-btn" data-field="otaReferenceNumber" title="编辑">✏️</button>
                                                    </div>
                                                </div>
                                                <div class="form-group editable-field">
                                                    <label for="carTypeId">车型 *</label>
                                                    <div class="field-container">
                                                        <select id="carTypeId" required>
                                                            <option value="">请选择车型</option>
                                                            <option value="1" selected>Comfort 5 Seater</option>
                                                            <option value="2">Premium 7 Seater</option>
                                                        </select>
                                                        <button type="button" class="edit-field-btn" data-field="carTypeId" title="编辑">✏️</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 客户信息 -->
                                        <div class="form-section">
                                            <h4>客户信息</h4>
                                            <div class="form-grid">
                                                <div class="form-group editable-field">
                                                    <label for="customerName">客户姓名</label>
                                                    <div class="field-container">
                                                        <input type="text" id="customerName" placeholder="客户姓名" value="张三">
                                                        <button type="button" class="edit-field-btn" data-field="customerName" title="编辑">✏️</button>
                                                    </div>
                                                </div>
                                                <div class="form-group editable-field">
                                                    <label for="customerContact">联系电话</label>
                                                    <div class="field-container">
                                                        <input type="tel" id="customerContact" placeholder="+60123456789" value="+60123456789">
                                                        <button type="button" class="edit-field-btn" data-field="customerContact" title="编辑">✏️</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 行程信息 -->
                                        <div class="form-section">
                                            <h4>行程信息</h4>
                                            <div class="form-grid">
                                                <div class="form-group editable-field">
                                                    <label for="pickup">上车地点</label>
                                                    <div class="field-container">
                                                        <input type="text" id="pickup" placeholder="上车地点" value="KLIA2机场">
                                                        <button type="button" class="edit-field-btn" data-field="pickup" title="编辑">✏️</button>
                                                    </div>
                                                </div>
                                                <div class="form-group editable-field">
                                                    <label for="destination">目的地</label>
                                                    <div class="field-container">
                                                        <input type="text" id="destination" placeholder="目的地" value="吉隆坡双子塔">
                                                        <button type="button" class="edit-field-btn" data-field="destination" title="编辑">✏️</button>
                                                    </div>
                                                </div>
                                                <div class="form-group editable-field">
                                                    <label for="date">日期</label>
                                                    <div class="field-container">
                                                        <input type="date" id="date" value="2024-03-15">
                                                        <button type="button" class="edit-field-btn" data-field="date" title="编辑">✏️</button>
                                                    </div>
                                                </div>
                                                <div class="form-group editable-field">
                                                    <label for="time">时间</label>
                                                    <div class="field-container">
                                                        <input type="time" id="time" value="14:30">
                                                        <button type="button" class="edit-field-btn" data-field="time" title="编辑">✏️</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 操作按钮 -->
                                        <div class="form-actions">
                                            <button type="button" id="previewOrder" class="btn btn-secondary">预览订单</button>
                                            <button type="submit" id="createOrder" class="btn btn-primary">
                                                <span class="btn-text">创建订单</span>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <script>
        // 简单的测试脚本
        document.addEventListener('DOMContentLoaded', function() {
            const testShowModal = document.getElementById('testShowModal');
            const testHideModal = document.getElementById('testHideModal');
            const closePreviewModal = document.getElementById('closePreviewModal');
            const previewSection = document.getElementById('previewSection');
            const previewModalOverlay = document.getElementById('previewModalOverlay');

            // 显示浮窗
            function showPreviewModal() {
                if (previewSection) {
                    previewSection.classList.add('show-preview');
                    previewSection.style.display = 'block';
                    document.body.style.overflow = 'hidden';
                }
            }

            // 隐藏浮窗
            function hidePreviewModal() {
                if (previewSection) {
                    previewSection.classList.remove('show-preview');
                    previewSection.style.display = 'none';
                    document.body.style.overflow = '';
                }
            }

            // 处理字段编辑
            function handleFieldEdit(button) {
                const fieldName = button.getAttribute('data-field');
                const fieldGroup = button.closest('.editable-field');
                const field = document.getElementById(fieldName);
                
                if (!field || !fieldGroup) return;
                
                if (fieldGroup.classList.contains('editing')) {
                    // 保存编辑
                    fieldGroup.classList.remove('editing');
                    alert(`字段 ${fieldName} 已保存: ${field.value}`);
                } else {
                    // 开始编辑
                    fieldGroup.classList.add('editing');
                    field.focus();
                }
            }

            // 绑定事件
            testShowModal?.addEventListener('click', showPreviewModal);
            testHideModal?.addEventListener('click', hidePreviewModal);
            closePreviewModal?.addEventListener('click', hidePreviewModal);
            
            // 点击遮罩关闭
            previewModalOverlay?.addEventListener('click', (e) => {
                if (e.target === previewModalOverlay) {
                    hidePreviewModal();
                }
            });

            // 编辑按钮事件委托
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('edit-field-btn')) {
                    handleFieldEdit(e.target);
                }
            });

            console.log('UI重构测试页面已加载');
        });
    </script>
</body>
</html>
