<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单功能修复测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-input {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-family: monospace;
        }
        .test-button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #005a9e;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 多订单功能修复测试</h1>
        
        <div class="test-case">
            <h3>测试用例1：多日期订单</h3>
            <textarea class="test-input" id="test1">客户：张三先生 +60123456789
订单1：明天 14:30 KLIA2机场 到 Pavilion KL购物中心
订单2：后天 16:00 酒店 到 KLIA2机场
人数：2大1小</textarea>
            <button class="test-button" onclick="testMultiOrder('test1', '多日期订单')">测试多日期检测</button>
            <div id="result1" class="test-result"></div>
        </div>

        <div class="test-case">
            <h3>测试用例2：明确分隔的订单</h3>
            <textarea class="test-input" id="test2">订单1：
客户：李四 +60987654321
时间：2024-01-15 10:00
路线：机场到酒店

---

订单2：
客户：王五 +60111222333
时间：2024-01-16 15:30
路线：酒店到购物中心</textarea>
            <button class="test-button" onclick="testMultiOrder('test2', '分隔符订单')">测试分隔符检测</button>
            <div id="result2" class="test-result"></div>
        </div>

        <div class="test-case">
            <h3>测试用例3：编号订单</h3>
            <textarea class="test-input" id="test3">第1个订单：接机服务 KLIA2 到 吉隆坡市中心
第2个订单：送机服务 酒店 到 KLIA
第3个订单：包车服务 全天游览</textarea>
            <button class="test-button" onclick="testMultiOrder('test3', '编号订单')">测试编号检测</button>
            <div id="result3" class="test-result"></div>
        </div>

        <div class="test-case">
            <h3>测试用例4：单订单（对照组）</h3>
            <textarea class="test-input" id="test4">客户：赵六先生 +60555666777
接送：KLIA2机场 到 Pavilion KL购物中心
时间：明天 14:30
人数：2大1小</textarea>
            <button class="test-button" onclick="testMultiOrder('test4', '单订单')">测试单订单</button>
            <div id="result4" class="test-result"></div>
        </div>

        <div class="test-case">
            <h3>系统状态检查</h3>
            <button class="test-button" onclick="checkSystemStatus()">检查系统状态</button>
            <div id="systemStatus" class="test-result"></div>
        </div>
    </div>

    <script>
        // 模拟多订单管理器（简化版本用于测试）
        class TestMultiOrderManager {
            constructor() {
                this.datePatterns = [
                    /\d{4}-\d{2}-\d{2}/g,
                    /\d{1,2}\/\d{1,2}\/\d{4}/g,
                    /\d{1,2}-\d{1,2}-\d{4}/g,
                    /(明天|后天|大后天)/g,
                    /(今天|今日)/g,
                    /(周一|周二|周三|周四|周五|周六|周日|星期一|星期二|星期三|星期四|星期五|星期六|星期日)/g
                ];
                
                this.orderSeparators = [
                    /---+/g,
                    /===+/g,
                    /订单\s*\d+/gi,
                    /order\s*\d+/gi
                ];
            }

            detectMultipleDates(text) {
                const allDates = [];
                this.datePatterns.forEach(pattern => {
                    const matches = text.match(pattern);
                    if (matches) {
                        allDates.push(...matches);
                    }
                });
                return [...new Set(allDates)];
            }

            isMultiOrderMode(text) {
                if (!text || typeof text !== 'string') {
                    return false;
                }

                // 检测多个日期
                const dates = this.detectMultipleDates(text);
                if (dates.length > 1) {
                    return true;
                }

                // 检测订单分隔符
                const hasOrderSeparators = this.orderSeparators.some(pattern => {
                    const matches = text.match(pattern);
                    return matches && matches.length > 1;
                });

                if (hasOrderSeparators) {
                    return true;
                }

                // 检测多个明确的订单标识
                const orderKeywords = [
                    /订单\s*[1-9]/gi,
                    /order\s*[1-9]/gi,
                    /第[一二三四五六七八九十]\s*个/gi,
                    /第\s*[1-9]\s*个/gi
                ];

                const hasMultipleOrderKeywords = orderKeywords.some(pattern => {
                    const matches = text.match(pattern);
                    return matches && matches.length > 1;
                });

                return hasMultipleOrderKeywords;
            }

            splitOrderText(text) {
                if (!text || typeof text !== 'string') {
                    return [text];
                }

                if (!this.isMultiOrderMode(text)) {
                    return [text];
                }

                let segments = [text];

                // 按分隔符分割
                this.orderSeparators.forEach(pattern => {
                    const newSegments = [];
                    segments.forEach(segment => {
                        const parts = segment.split(pattern);
                        newSegments.push(...parts.filter(part => part.trim()));
                    });
                    if (newSegments.length > segments.length) {
                        segments = newSegments;
                    }
                });

                return segments.filter(segment => segment.trim().length > 10);
            }
        }

        const testManager = new TestMultiOrderManager();

        function testMultiOrder(inputId, testName) {
            const input = document.getElementById(inputId);
            const result = document.getElementById('result' + inputId.slice(-1));
            const text = input.value;

            try {
                const isMultiOrder = testManager.isMultiOrderMode(text);
                const dates = testManager.detectMultipleDates(text);
                const segments = testManager.splitOrderText(text);

                let output = `=== ${testName} 测试结果 ===\n\n`;
                output += `📝 输入文本长度: ${text.length} 字符\n`;
                output += `🔍 多订单检测结果: ${isMultiOrder ? '✅ 是' : '❌ 否'}\n`;
                output += `📅 检测到的日期: ${dates.length > 0 ? dates.join(', ') : '无'}\n`;
                output += `📋 分割后的订单数量: ${segments.length}\n\n`;

                if (segments.length > 1) {
                    output += `📄 分割后的订单片段:\n`;
                    segments.forEach((segment, index) => {
                        output += `\n--- 订单 ${index + 1} ---\n`;
                        output += segment.trim() + '\n';
                    });
                }

                result.className = 'test-result ' + (isMultiOrder ? 'success' : 'info');
                result.textContent = output;

            } catch (error) {
                result.className = 'test-result error';
                result.textContent = `❌ 测试失败: ${error.message}`;
            }
        }

        function checkSystemStatus() {
            const result = document.getElementById('systemStatus');
            
            try {
                let output = `=== 系统状态检查 ===\n\n`;
                
                // 检查浏览器环境
                output += `🌐 浏览器: ${navigator.userAgent.split(' ')[0]}\n`;
                output += `📱 设备: ${navigator.platform}\n`;
                output += `🔧 JavaScript版本: ES${typeof Symbol !== 'undefined' ? '6+' : '5'}\n\n`;
                
                // 检查测试管理器
                output += `🧪 测试管理器状态:\n`;
                output += `  - 日期模式数量: ${testManager.datePatterns.length}\n`;
                output += `  - 分隔符模式数量: ${testManager.orderSeparators.length}\n`;
                output += `  - 多订单检测方法: ${typeof testManager.isMultiOrderMode}\n`;
                output += `  - 文本分割方法: ${typeof testManager.splitOrderText}\n\n`;
                
                output += `✅ 系统状态正常，可以进行测试`;
                
                result.className = 'test-result success';
                result.textContent = output;
                
            } catch (error) {
                result.className = 'test-result error';
                result.textContent = `❌ 系统检查失败: ${error.message}`;
            }
        }

        // 页面加载完成后自动检查系统状态
        window.addEventListener('load', checkSystemStatus);
    </script>
</body>
</html>
