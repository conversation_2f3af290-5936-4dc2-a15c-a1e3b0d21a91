<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA订单处理系统 - GoMyHire Integration</title>
    <link rel="stylesheet" href="style.css">
    <!-- 图标已移除，使用浏览器默认图标 -->
</head>
<body>
    <div id="app">
        <!-- 顶部导航栏 -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <span class="title-icon">🚗</span>
                    OTA订单处理系统
                </h1>
                <div class="header-controls">
                    <div class="user-info" id="userInfo" style="display: none;">
                        <span id="currentUser"></span>
                        <button id="logoutBtn" class="btn btn-outline">退出登录</button>
                    </div>
                    <div class="theme-toggle">
                        <button id="themeToggle" class="btn btn-icon" title="切换主题">🌙</button>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主要内容区 -->
        <main class="main-content">
            <!-- 登录面板 -->
            <div id="loginPanel" class="login-panel">
                <div class="login-card">
                    <h2>系统登录</h2>
                    <form id="loginForm">
                        <div class="form-group">
                            <label for="email">邮箱</label>
                            <input type="email" id="email" value="" required>
                        </div>
                        <div class="form-group">
                            <label for="password">密码</label>
                            <input type="password" id="password" value="" required>
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="rememberMe" checked>
                                <span class="checkbox-text">保持登录</span>
                            </label>
                        </div>
                        <div class="login-actions">
                            <button type="submit" class="btn btn-primary" id="loginBtn">
                                <span class="btn-text">登录</span>
                                <span class="loading-spinner" style="display: none;">⏳</span>
                            </button>
                            <button type="button" class="btn btn-outline btn-sm" id="clearSavedBtn" style="display: none;">
                                清除保存的账号
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 工作区 -->
            <div id="workspace" class="workspace" style="display: none;">
                <!-- 智能输入区 -->
                <section class="input-section">
                    <div class="section-header">
                        <h3>🤖 智能订单解析 (实时分析)</h3>
                        <div class="section-controls">
                            <button id="clearInput" class="btn btn-outline btn-sm">清空</button>
                            <button id="sampleInput" class="btn btn-outline btn-sm">示例数据</button>
                            <button id="parseBtn" class="btn btn-secondary btn-sm" title="手动解析（备用）">手动解析</button>
                            <button id="debugUserBtn" class="btn btn-outline btn-sm" style="background: red; color: white;">调试用户信息</button>
                        </div>
                    </div>
                    <div class="input-card">
                        <div class="form-group">
                            <label for="orderInput">订单描述（支持自然语言，自动实时分析）</label>
                            <textarea 
                                id="orderInput" 
                                placeholder="请输入订单信息，系统将自动分析...&#10;&#10;例如：&#10;客户：张三 +60123456789&#10;接送：KLIA2机场 到 吉隆坡双子塔&#10;时间：2024-03-15 14:30&#10;人数：3人&#10;要求：需要儿童座椅"
                                rows="6"></textarea>
                        </div>
                        <div class="input-actions">
                            <div class="realtime-info">
                                <span class="realtime-badge">🔄 实时分析</span>
                                <small>输入内容将自动分析，无需手动点击</small>
                            </div>
                            <div class="gemini-status" id="geminiStatus"></div>
                        </div>
                    </div>
                </section>

                <!-- 订单预览区 - 重构为浮窗模式 -->
                <section class="preview-section" id="previewSection" style="display: none;">
                    <!-- 浮窗遮罩 -->
                    <div class="preview-modal-overlay" id="previewModalOverlay">
                        <div class="preview-modal-content">
                            <div class="preview-modal-header">
                                <h3>📋 订单预览与编辑</h3>
                                <div class="preview-modal-controls">
                                    <button id="validateOrder" class="btn btn-outline btn-sm">验证数据</button>
                                    <button id="resetOrder" class="btn btn-outline btn-sm">重置</button>
                                    <button id="closePreviewModal" class="btn btn-icon" title="关闭">✕</button>
                                </div>
                            </div>
                            <div class="preview-modal-body">
                                <div class="preview-card">
                        <form id="orderForm" class="order-form">
                            <!-- 基本信息 -->
                            <div class="form-section">
                                <h4>基本信息</h4>
                                <div class="form-grid">
                                    <div class="form-group editable-field">
                                        <label for="subCategoryId">子分类 *</label>
                                        <div class="field-container">
                                            <select id="subCategoryId" required>
                                                <option value="">请选择子分类</option>
                                            </select>
                                            <button type="button" class="edit-field-btn" data-field="subCategoryId" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                    <div class="form-group editable-field">
                                        <label for="otaReferenceNumber">OTA参考号 *</label>
                                        <div class="field-container">
                                            <input type="text" id="otaReferenceNumber" placeholder="GMH-" required>
                                            <button type="button" class="edit-field-btn" data-field="otaReferenceNumber" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                    <!-- 新增：OTA渠道选择与自定义输入 -->
                                    <div class="form-group">
                                        <label for="otaChannel">OTA渠道</label>
                                        <div style="display: flex; gap: 8px;">
                                            <select id="otaChannel" style="flex: 1;">
                                                <option value="">请选择OTA渠道</option>
                                                <!-- 选项由js动态填充 -->
                                            </select>
                                            <input type="text" id="otaChannelCustom" placeholder="自定义OTA" style="flex: 1;">
                                        </div>
                                        <small>可从下拉选择或自定义输入OTA渠道</small>
                                    </div>
                                    <div class="form-group editable-field">
                                        <label for="carTypeId">车型 *</label>
                                        <div class="field-container">
                                            <select id="carTypeId" required>
                                                <option value="">请选择车型</option>
                                            </select>
                                            <button type="button" class="edit-field-btn" data-field="carTypeId" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                    <!-- 负责人字段已隐藏，系统根据登录邮箱自动匹配 -->
                                    <div class="form-group" style="display: none;">
                                        <label for="inchargeByBackendUserId">负责人 *</label>
                                        <select id="inchargeByBackendUserId" required>
                                            <option value="">请选择负责人</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- 客户信息 -->
                            <div class="form-section">
                                <h4>客户信息</h4>
                                <div class="form-grid">
                                    <div class="form-group editable-field">
                                        <label for="customerName">客户姓名</label>
                                        <div class="field-container">
                                            <input type="text" id="customerName" placeholder="客户姓名">
                                            <button type="button" class="edit-field-btn" data-field="customerName" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                    <div class="form-group editable-field">
                                        <label for="customerContact">联系电话</label>
                                        <div class="field-container">
                                            <input type="tel" id="customerContact" placeholder="+60123456789">
                                            <button type="button" class="edit-field-btn" data-field="customerContact" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                    <div class="form-group editable-field">
                                        <label for="customerEmail">客户邮箱</label>
                                        <div class="field-container">
                                            <input type="email" id="customerEmail" placeholder="<EMAIL>">
                                            <button type="button" class="edit-field-btn" data-field="customerEmail" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                    <div class="form-group editable-field">
                                        <label for="flightInfo">航班信息</label>
                                        <div class="field-container">
                                            <input type="text" id="flightInfo" placeholder="MH123">
                                            <button type="button" class="edit-field-btn" data-field="flightInfo" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 行程信息 -->
                            <div class="form-section">
                                <h4>行程信息</h4>
                                <div class="form-grid">
                                    <div class="form-group editable-field">
                                        <label for="pickup">上车地点</label>
                                        <div class="field-container">
                                            <input type="text" id="pickup" placeholder="上车地点">
                                            <button type="button" class="edit-field-btn" data-field="pickup" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                    <div class="form-group editable-field">
                                        <label for="destination">目的地</label>
                                        <div class="field-container">
                                            <input type="text" id="destination" placeholder="目的地">
                                            <button type="button" class="edit-field-btn" data-field="destination" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                    <div class="form-group editable-field">
                                        <label for="date">日期</label>
                                        <div class="field-container">
                                            <input type="date" id="date">
                                            <button type="button" class="edit-field-btn" data-field="date" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                    <div class="form-group editable-field">
                                        <label for="time">时间</label>
                                        <div class="field-container">
                                            <input type="time" id="time">
                                            <button type="button" class="edit-field-btn" data-field="time" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 服务配置 -->
                            <div class="form-section">
                                <h4>服务配置</h4>
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="passengerNumber">乘客人数</label>
                                        <input type="number" id="passengerNumber" min="1" max="50" placeholder="3">
                                    </div>
                                    <div class="form-group">
                                        <label for="luggageNumber">行李件数</label>
                                        <input type="number" id="luggageNumber" min="0" max="50" placeholder="2">
                                    </div>
                                    <div class="form-group">
                                        <label for="drivingRegionId">行驶区域</label>
                                        <select id="drivingRegionId">
                                            <option value="">自动选择</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="languagesIdArray">语言要求</label>
                                        <select id="languagesIdArray" multiple>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- 特殊要求 -->
                            <div class="form-section">
                                <h4>特殊要求与费用</h4>
                                <div class="form-grid">
                                    <div class="form-group checkbox-group">
                                        <label>
                                            <input type="checkbox" id="tourGuide">
                                            导游服务
                                        </label>
                                    </div>
                                    <div class="form-group checkbox-group">
                                        <label>
                                            <input type="checkbox" id="babyChair">
                                            儿童座椅
                                        </label>
                                    </div>
                                    <div class="form-group checkbox-group">
                                        <label>
                                            <input type="checkbox" id="meetAndGreet">
                                            接机服务
                                        </label>
                                    </div>
                                    <div class="form-group">
                                        <label for="otaPrice">OTA价格</label>
                                        <input type="number" id="otaPrice" step="0.01" placeholder="0.00">
                                    </div>
                                    <div class="form-group">
                                        <label for="driverFee">司机费用</label>
                                        <input type="number" id="driverFee" step="0.01" placeholder="0.00">
                                    </div>
                                    <div class="form-group">
                                        <label for="driverCollect">司机代收</label>
                                        <input type="number" id="driverCollect" step="0.01" placeholder="0.00">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="extraRequirement">额外要求</label>
                                    <textarea id="extraRequirement" rows="3" placeholder="其他特殊要求..."></textarea>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="form-actions">
                                <button type="button" id="previewOrder" class="btn btn-secondary">预览订单</button>
                                <button type="submit" id="createOrder" class="btn btn-primary">
                                    <span class="btn-text">创建订单</span>
                                    <span class="loading-spinner" style="display: none;">⏳</span>
                                </button>
                            </div>
                        </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 日志控制台 -->
                <!-- 已移除日志控制台前端显示，仅保留后台调试控制台输出 -->
            </div>
        </main>

        <!-- 状态栏 -->
        <footer class="status-bar">
            <div class="status-info">
                <span id="connectionStatus" class="status-item">🔌 未连接</span>
                <span id="dataStatus" class="status-item">📊 等待数据</span>
                <span id="lastUpdate" class="status-item">⏰ --:--</span>
            </div>
        </footer>

        <!-- 模态框 -->
        <div id="modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalTitle">标题</h3>
                    <button id="modalClose" class="btn btn-icon">✕</button>
                </div>
                <div id="modalBody" class="modal-body">内容</div>
                <div class="modal-footer">
                    <button id="modalCancel" class="btn btn-outline">取消</button>
                    <button id="modalConfirm" class="btn btn-primary">确认</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript模块 - 传统script标签加载方式 -->
    <!-- 注意：加载顺序很重要，依赖的模块必须先加载 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/ota-channel-mapping.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/ui-manager.js"></script>
    <script src="main.js"></script>
</body>
</html>