<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA订单处理系统 - GoMyHire Integration</title>
    <link rel="stylesheet" href="style.css">
    <!-- 图标已移除，使用浏览器默认图标 -->
</head>
<body>
    <div id="app">
        <!-- 顶部导航栏 -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <span class="title-icon">🚗</span>
                    <span data-i18n="header.title">OTA订单处理系统</span>
                </h1>
                <div class="header-controls">
                    <div class="persistent-email" id="persistentEmailContainer" style="display: none;">
                        <label for="persistentEmail" data-i18n="header.defaultEmail">默认邮箱:</label>
                        <input type="email" id="persistentEmail" data-i18n="header.defaultEmailPlaceholder" placeholder="设置默认客户邮箱" data-i18n-title="header.defaultEmailTooltip" title="设置默认客户邮箱，当AI解析无法获取邮箱时自动使用">
                        <button type="button" id="saveEmailBtn" class="btn btn-icon" data-i18n-title="common.save" title="保存邮箱">💾</button>
                    </div>
                    <div class="user-info" id="userInfo" style="display: none;">
                        <span id="currentUser"></span>
                        <button type="button" id="historyBtn" class="btn btn-outline" data-i18n="header.historyOrders">历史订单</button>
                        <button type="button" id="logoutBtn" class="btn btn-outline" data-i18n="header.logout">退出登录</button>
                    </div>
                    <div class="theme-toggle">
                        <button type="button" id="languageToggle" class="btn btn-icon" data-i18n-title="header.language" title="中/EN">中/EN</button>
                        <button type="button" id="themeToggle" class="btn btn-icon" data-i18n-title="header.toggleTheme" title="切换主题">🌙</button>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主要内容区 -->
        <main class="main-content">
            <!-- 登录面板 -->
            <div id="loginPanel" class="login-panel">
                <div class="login-card">
                    <h2 data-i18n="login.title">系统登录</h2>
                    <form id="loginForm">
                        <div class="form-group">
                            <label for="email" data-i18n="login.email">邮箱</label>
                            <input type="email" id="email" value="" data-i18n="login.emailPlaceholder" placeholder="请输入邮箱地址" required>
                        </div>
                        <div class="form-group">
                            <label for="password" data-i18n="login.password">密码</label>
                            <input type="password" id="password" value="" data-i18n="login.passwordPlaceholder" placeholder="请输入密码" required>
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="rememberMe" checked>
                                <span class="checkbox-text" data-i18n="login.rememberMe">保持登录</span>
                            </label>
                        </div>
                        <div class="login-actions">
                            <button type="submit" class="btn btn-primary" id="loginBtn">
                                <span class="btn-text" data-i18n="login.loginButton">登录</span>
                                <span class="loading-spinner hidden">⏳</span>
                            </button>
                            <button type="button" class="btn btn-outline btn-sm hidden" id="clearSavedBtn" data-i18n="login.clearSaved">
                                清除保存的账号
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 工作区 -->
            <div id="workspace" class="workspace" style="display: none;">
                <!-- 智能输入区 -->
                <section class="input-section">
                    <div class="section-header">
                        <h3 data-i18n="input.title">🤖 智能订单解析 (实时分析)</h3>
                        <div class="section-controls">
                            <button type="button" id="clearInput" class="btn btn-outline btn-sm" data-i18n="input.clearButton">清空</button>
                            <button type="button" id="sampleInput" class="btn btn-outline btn-sm" data-i18n="input.sampleButton">示例数据</button>
                            <button type="button" id="parseBtn" class="btn btn-secondary btn-sm" data-i18n="input.parseButton" title="手动解析（备用）">手动解析</button>
                        </div>
                    </div>
                    <div class="input-card">
                        <div class="form-group">
                            <label for="orderInput" data-i18n="input.title">订单描述（支持自然语言，自动实时分析）</label>
                            <textarea
                                id="orderInput"
                                data-i18n="input.placeholder"
                                placeholder="请输入订单信息，系统将自动分析...&#10;&#10;例如：&#10;客户：张三 +60123456789&#10;接送：KLIA2机场 到 吉隆坡双子塔&#10;时间：2024-03-15 14:30&#10;人数：3人&#10;要求：需要儿童座椅"
                                rows="6"></textarea>
                        </div>
                        <div class="input-actions">
                            <div class="realtime-info">
                                <span class="realtime-badge">🔄 实时分析</span>
                                <small>输入内容将自动分析，无需手动点击</small>
                            </div>
                            <div class="gemini-status" id="geminiStatus"></div>
                        </div>
                    </div>
                </section>

                <!-- 订单预览区 - 重构为浮窗模式 -->
                <section class="preview-section hidden" id="previewSection">
                    <!-- 浮窗遮罩 -->
                    <div class="preview-modal-overlay" id="previewModalOverlay">
                        <div class="preview-modal-content">
                            <div class="preview-modal-header">
                                <h3>📋 订单预览与编辑</h3>
                                <div class="preview-modal-controls">
                                    <button type="button" id="validateOrder" class="btn btn-outline btn-sm">验证数据</button>
                                    <button type="button" id="resetOrder" class="btn btn-outline btn-sm">重置</button>
                                    <button type="button" id="closePreviewModal" class="btn btn-icon" title="关闭">✕</button>
                                </div>
                            </div>
                            <div class="preview-modal-body">
                                <div class="preview-card">
                        <form id="orderForm" class="order-form">
                            <!-- 基本信息 -->
                            <div class="form-section">
                                <h4>基本信息</h4>
                                <div class="form-grid">
                                    <div class="form-group editable-field">
                                        <label for="subCategoryId">子分类 *</label>
                                        <div class="field-container">
                                            <select id="subCategoryId" required>
                                                <option value="">请选择子分类</option>
                                            </select>
                                            <button type="button" class="edit-field-btn" data-field="subCategoryId" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                    <div class="form-group editable-field">
                                        <label for="otaReferenceNumber">OTA参考号 *</label>
                                        <div class="field-container">
                                            <input type="text" id="otaReferenceNumber" placeholder="GMH-" required>
                                            <button type="button" class="edit-field-btn" data-field="otaReferenceNumber" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                    <!-- OTA渠道选择与自定义输入 -->
                                    <div class="form-group editable-field">
                                        <label for="otaChannel">OTA渠道</label>
                                        <div class="field-container">
                                            <div class="ota-channel-inputs">
                                                <select id="otaChannel">
                                                    <option value="">请选择OTA渠道</option>
                                                    <!-- 选项由js动态填充 -->
                                                </select>
                                                <input type="text" id="otaChannelCustom" placeholder="自定义OTA">
                                            </div>
                                            <button type="button" class="edit-field-btn" data-field="otaChannel" title="编辑">✏️</button>
                                        </div>
                                        <small>可从下拉选择或自定义输入OTA渠道</small>
                                    </div>
                                    <div class="form-group editable-field">
                                        <label for="carTypeId">车型 *</label>
                                        <div class="field-container">
                                            <select id="carTypeId" required>
                                                <option value="">请选择车型</option>
                                            </select>
                                            <button type="button" class="edit-field-btn" data-field="carTypeId" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                    <!-- 负责人字段已隐藏，系统根据登录邮箱自动匹配 -->
                                    <div class="form-group hidden-field">
                                        <label for="inchargeByBackendUserId">负责人 *</label>
                                        <select id="inchargeByBackendUserId" required>
                                            <option value="">请选择负责人</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- 客户信息 -->
                            <div class="form-section">
                                <h4>客户信息</h4>
                                <div class="form-grid">
                                    <div class="form-group editable-field">
                                        <label for="customerName">客户姓名</label>
                                        <div class="field-container">
                                            <input type="text" id="customerName" placeholder="客户姓名">
                                            <button type="button" class="edit-field-btn" data-field="customerName" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                    <div class="form-group editable-field">
                                        <label for="customerContact">联系电话</label>
                                        <div class="field-container">
                                            <input type="tel" id="customerContact" placeholder="+60123456789">
                                            <button type="button" class="edit-field-btn" data-field="customerContact" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                    <div class="form-group editable-field">
                                        <label for="customerEmail">客户邮箱</label>
                                        <div class="field-container">
                                            <input type="email" id="customerEmail" placeholder="<EMAIL>">
                                            <button type="button" class="edit-field-btn" data-field="customerEmail" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                    <div class="form-group editable-field">
                                        <label for="flightInfo">航班信息</label>
                                        <div class="field-container">
                                            <input type="text" id="flightInfo" placeholder="MH123">
                                            <button type="button" class="edit-field-btn" data-field="flightInfo" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 行程信息 -->
                            <div class="form-section">
                                <h4>行程信息</h4>
                                <div class="form-grid">
                                    <div class="form-group editable-field">
                                        <label for="pickup">上车地点</label>
                                        <div class="field-container">
                                            <input type="text" id="pickup" placeholder="上车地点">
                                            <button type="button" class="edit-field-btn" data-field="pickup" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                    <div class="form-group editable-field">
                                        <label for="destination">目的地</label>
                                        <div class="field-container">
                                            <input type="text" id="destination" placeholder="目的地">
                                            <button type="button" class="edit-field-btn" data-field="destination" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                    <div class="form-group editable-field">
                                        <label for="date">日期</label>
                                        <div class="field-container">
                                            <input type="date" id="date">
                                            <button type="button" class="edit-field-btn" data-field="date" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                    <div class="form-group editable-field">
                                        <label for="time">时间</label>
                                        <div class="field-container">
                                            <input type="time" id="time">
                                            <button type="button" class="edit-field-btn" data-field="time" title="编辑">✏️</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 服务配置 -->
                            <div class="form-section">
                                <h4>服务配置</h4>
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="passengerNumber">乘客人数</label>
                                        <input type="number" id="passengerNumber" min="1" max="50" placeholder="3">
                                    </div>
                                    <div class="form-group">
                                        <label for="luggageNumber">行李件数</label>
                                        <input type="number" id="luggageNumber" min="0" max="50" placeholder="2">
                                    </div>
                                    <div class="form-group">
                                        <label for="drivingRegionId">行驶区域</label>
                                        <select id="drivingRegionId">
                                            <option value="">自动选择</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="languagesIdArray">语言要求</label>
                                        <select id="languagesIdArray" multiple>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- 特殊要求 -->
                            <div class="form-section">
                                <h4>特殊要求与费用</h4>
                                <div class="form-grid">
                                    <div class="form-group checkbox-group">
                                        <label>
                                            <input type="checkbox" id="tourGuide">
                                            导游服务
                                        </label>
                                    </div>
                                    <div class="form-group checkbox-group">
                                        <label>
                                            <input type="checkbox" id="babyChair">
                                            儿童座椅
                                        </label>
                                    </div>
                                    <div class="form-group checkbox-group">
                                        <label>
                                            <input type="checkbox" id="meetAndGreet">
                                            接机服务
                                        </label>
                                    </div>
                                    <div class="form-group">
                                        <label for="otaPrice">OTA价格</label>
                                        <input type="number" id="otaPrice" step="0.01" placeholder="0.00">
                                    </div>
                                    <div class="form-group">
                                        <label for="driverFee">司机费用</label>
                                        <input type="number" id="driverFee" step="0.01" placeholder="0.00">
                                    </div>
                                    <div class="form-group">
                                        <label for="driverCollect">司机代收</label>
                                        <input type="number" id="driverCollect" step="0.01" placeholder="0.00">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="extraRequirement">额外要求</label>
                                    <textarea id="extraRequirement" rows="3" placeholder="其他特殊要求..."></textarea>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="form-actions">
                                <button type="button" id="previewOrder" class="btn btn-secondary">预览订单</button>
                                <button type="submit" id="createOrder" class="btn btn-primary">
                                    <span class="btn-text">创建订单</span>
                                    <span class="loading-spinner hidden">⏳</span>
                                </button>
                            </div>
                        </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 日志控制台 -->
                <!-- 已移除日志控制台前端显示，仅保留后台调试控制台输出 -->
            </div>
        </main>

        <!-- 状态栏 -->
        <footer class="status-bar">
            <div class="status-info">
                <span id="connectionStatus" class="status-item">🔌 未连接</span>
                <span id="dataStatus" class="status-item">📊 等待数据</span>
                <span id="lastUpdate" class="status-item">⏰ --:--</span>
            </div>
        </footer>

        <!-- 模态框 -->
        <div id="modal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalTitle">标题</h3>
                    <button type="button" id="modalClose" class="btn btn-icon">✕</button>
                </div>
                <div id="modalBody" class="modal-body">内容</div>
                <div class="modal-footer">
                    <button type="button" id="modalCancel" class="btn btn-outline">取消</button>
                    <button type="button" id="modalConfirm" class="btn btn-primary">确认</button>
                </div>
            </div>
        </div>

        <!-- 历史订单面板 -->
        <div id="historyPanel" class="history-panel hidden">
            <div class="history-overlay">
                <div class="history-content">
                    <div class="history-header">
                        <h3>📋 历史订单管理</h3>
                        <div class="history-controls">
                            <button type="button" id="exportHistoryBtn" class="btn btn-outline btn-sm">导出</button>
                            <button type="button" id="clearHistoryBtn" class="btn btn-outline btn-sm">清空</button>
                            <button type="button" id="closeHistoryBtn" class="btn btn-icon">✕</button>
                        </div>
                    </div>

                    <div class="history-search">
                        <div class="search-grid">
                            <div class="search-group">
                                <label for="searchOrderId">订单ID</label>
                                <input type="text" id="searchOrderId" placeholder="搜索订单ID">
                            </div>
                            <div class="search-group">
                                <label for="searchCustomer">客户姓名</label>
                                <input type="text" id="searchCustomer" placeholder="搜索客户姓名">
                            </div>
                            <div class="search-group">
                                <label for="searchDateFrom">开始日期</label>
                                <input type="date" id="searchDateFrom">
                            </div>
                            <div class="search-group">
                                <label for="searchDateTo">结束日期</label>
                                <input type="date" id="searchDateTo">
                            </div>
                        </div>
                        <div class="search-actions">
                            <button type="button" id="searchHistoryBtn" class="btn btn-primary btn-sm">搜索</button>
                            <button type="button" id="resetSearchBtn" class="btn btn-outline btn-sm">重置</button>
                        </div>
                    </div>

                    <div class="history-stats">
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-label">总计</span>
                                <span class="stat-value" id="statTotal">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">今日</span>
                                <span class="stat-value" id="statToday">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">本周</span>
                                <span class="stat-value" id="statWeek">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">本月</span>
                                <span class="stat-value" id="statMonth">0</span>
                            </div>
                        </div>
                    </div>

                    <div class="history-list">
                        <div class="list-header">
                            <div class="list-title">订单列表</div>
                            <div class="list-count">共 <span id="listCount">0</span> 条记录</div>
                        </div>
                        <div class="list-container" id="historyListContainer">
                            <div class="empty-state">
                                <div class="empty-icon">📝</div>
                                <div class="empty-text">暂无历史订单</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript模块 - 传统script标签加载方式 -->
    <!-- 注意：加载顺序很重要，依赖的模块必须先加载 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/ota-channel-mapping.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/order-history-manager.js"></script>
    <script src="js/i18n.js"></script>
    <script src="js/ui-manager.js"></script>
    <script src="main.js"></script>
</body>
</html>