<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单功能测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }

        .test-header h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 4px solid #007bff;
        }

        .test-section h2 {
            color: #007bff;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-case {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }

        .test-case h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .test-input {
            width: 100%;
            min-height: 120px;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-family: monospace;
            font-size: 14px;
            resize: vertical;
            margin-bottom: 10px;
        }

        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            transition: background 0.3s;
        }

        .test-button:hover {
            background: #0056b3;
        }

        .test-button.success {
            background: #28a745;
        }

        .test-button.danger {
            background: #dc3545;
        }

        .test-result {
            margin-top: 15px;
            padding: 12px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        .test-result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .test-result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .test-result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .test-stats {
            display: flex;
            justify-content: space-around;
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 多订单功能测试工具</h1>
            <p>测试多订单模式的检测、解析、验证、创建等核心功能</p>
        </div>

        <div class="test-stats">
            <div class="stat-item">
                <div class="stat-number" id="totalTests">0</div>
                <div class="stat-label">总测试数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="passedTests">0</div>
                <div class="stat-label">通过测试</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="failedTests">0</div>
                <div class="stat-label">失败测试</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="successRate">0%</div>
                <div class="stat-label">成功率</div>
            </div>
        </div>

        <!-- 多订单检测测试 -->
        <div class="test-section">
            <h2>🔍 多订单检测测试</h2>
            
            <div class="test-case">
                <h3>测试用例1：多日期检测</h3>
                <textarea class="test-input" id="multiDateInput" placeholder="输入包含多个日期的订单文本...">订单1：2024-01-15 从机场到酒店
订单2：2024-01-16 从酒店到景点
订单3：2024-01-17 返回机场</textarea>
                <button class="test-button" onclick="testMultiDateDetection()">测试多日期检测</button>
                <div class="test-result hidden" id="multiDateResult"></div>
            </div>

            <div class="test-case">
                <h3>测试用例2：订单分隔符检测</h3>
                <textarea class="test-input" id="separatorInput" placeholder="输入包含分隔符的订单文本...">订单1：机场接机
---
订单2：市区游览
===
订单3：送机服务</textarea>
                <button class="test-button" onclick="testSeparatorDetection()">测试分隔符检测</button>
                <div class="test-result hidden" id="separatorResult"></div>
            </div>
        </div>

        <!-- 订单解析测试 -->
        <div class="test-section">
            <h2>📝 订单解析测试</h2>
            
            <div class="test-case">
                <h3>测试用例3：完整订单解析</h3>
                <textarea class="test-input" id="parseInput" placeholder="输入完整的多订单文本...">订单1：2024-01-15 10:00 机场接机
客户：张三 电话：13800138000
从：吉隆坡国际机场 到：市中心酒店
乘客：2人 行李：3件

订单2：2024-01-16 14:00 市区包车
客户：李四 电话：13900139000  
从：酒店 到：双子塔
乘客：4人 行李：2件</textarea>
                <button class="test-button" onclick="testOrderParsing()">测试订单解析</button>
                <div class="test-result hidden" id="parseResult"></div>
            </div>
        </div>

        <!-- 批量操作测试 -->
        <div class="test-section">
            <h2>⚡ 批量操作测试</h2>
            
            <div class="test-case">
                <h3>测试用例4：批量验证</h3>
                <button class="test-button" onclick="testBatchValidation()">测试批量验证</button>
                <div class="test-result hidden" id="batchValidationResult"></div>
            </div>

            <div class="test-case">
                <h3>测试用例5：模拟批量创建</h3>
                <button class="test-button" onclick="testBatchCreation()">模拟批量创建</button>
                <div class="test-result hidden" id="batchCreationResult"></div>
            </div>
        </div>

        <!-- 性能测试 -->
        <div class="test-section">
            <h2>🚀 性能测试</h2>
            
            <div class="test-case">
                <h3>测试用例6：大批量数据处理</h3>
                <button class="test-button" onclick="testLargeBatch()">测试大批量处理</button>
                <div class="test-result hidden" id="largeBatchResult"></div>
            </div>

            <div class="test-case">
                <h3>测试用例7：内存使用监控</h3>
                <button class="test-button" onclick="testMemoryUsage()">测试内存使用</button>
                <div class="test-result hidden" id="memoryResult"></div>
            </div>
        </div>

        <!-- 错误处理测试 -->
        <div class="test-section">
            <h2>🛡️ 错误处理测试</h2>
            
            <div class="test-case">
                <h3>测试用例8：错误恢复机制</h3>
                <button class="test-button" onclick="testErrorRecovery()">测试错误恢复</button>
                <div class="test-result hidden" id="errorRecoveryResult"></div>
            </div>
        </div>

        <!-- 全部测试 -->
        <div class="test-section">
            <h2>🎯 综合测试</h2>
            <button class="test-button success" onclick="runAllTests()">运行所有测试</button>
            <button class="test-button danger" onclick="clearAllResults()">清除结果</button>
        </div>
    </div>

    <script>
        // 测试统计
        let testStats = {
            total: 0,
            passed: 0,
            failed: 0
        };

        // 更新测试统计
        function updateStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('passedTests').textContent = testStats.passed;
            document.getElementById('failedTests').textContent = testStats.failed;
            
            const successRate = testStats.total > 0 ? Math.round((testStats.passed / testStats.total) * 100) : 0;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        // 显示测试结果
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `test-result ${type}`;
            element.textContent = message;
            element.classList.remove('hidden');
            
            // 更新统计
            testStats.total++;
            if (type === 'success') {
                testStats.passed++;
            } else if (type === 'error') {
                testStats.failed++;
            }
            updateStats();
        }

        // 测试多日期检测
        function testMultiDateDetection() {
            const input = document.getElementById('multiDateInput').value;
            try {
                // 模拟多日期检测逻辑
                const datePattern = /\d{4}-\d{2}-\d{2}/g;
                const dates = input.match(datePattern);
                
                if (dates && dates.length > 1) {
                    showResult('multiDateResult', `✅ 检测到 ${dates.length} 个日期: ${dates.join(', ')}`, 'success');
                } else {
                    showResult('multiDateResult', '❌ 未检测到多个日期', 'error');
                }
            } catch (error) {
                showResult('multiDateResult', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 测试分隔符检测
        function testSeparatorDetection() {
            const input = document.getElementById('separatorInput').value;
            try {
                const separators = ['---', '===', '订单\\d+', 'order\\s*\\d+'];
                const foundSeparators = [];
                
                separators.forEach(sep => {
                    const regex = new RegExp(sep, 'gi');
                    if (regex.test(input)) {
                        foundSeparators.push(sep);
                    }
                });
                
                if (foundSeparators.length > 0) {
                    showResult('separatorResult', `✅ 检测到分隔符: ${foundSeparators.join(', ')}`, 'success');
                } else {
                    showResult('separatorResult', '❌ 未检测到订单分隔符', 'error');
                }
            } catch (error) {
                showResult('separatorResult', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 测试订单解析
        function testOrderParsing() {
            const input = document.getElementById('parseInput').value;
            try {
                // 模拟订单解析逻辑
                const orderPattern = /订单\d+/g;
                const orders = input.match(orderPattern);
                
                const phonePattern = /1[3-9]\d{9}/g;
                const phones = input.match(phonePattern);
                
                const datePattern = /\d{4}-\d{2}-\d{2}/g;
                const dates = input.match(datePattern);
                
                const result = {
                    orders: orders ? orders.length : 0,
                    phones: phones ? phones.length : 0,
                    dates: dates ? dates.length : 0
                };
                
                showResult('parseResult', `✅ 解析结果:\n订单数: ${result.orders}\n电话数: ${result.phones}\n日期数: ${result.dates}`, 'success');
            } catch (error) {
                showResult('parseResult', `❌ 解析失败: ${error.message}`, 'error');
            }
        }

        // 测试批量验证
        function testBatchValidation() {
            try {
                // 模拟批量验证
                const mockOrders = [
                    { id: 1, valid: true },
                    { id: 2, valid: false },
                    { id: 3, valid: true }
                ];
                
                const validCount = mockOrders.filter(o => o.valid).length;
                const invalidCount = mockOrders.length - validCount;
                
                showResult('batchValidationResult', `✅ 批量验证完成:\n有效订单: ${validCount}\n无效订单: ${invalidCount}`, 'success');
            } catch (error) {
                showResult('batchValidationResult', `❌ 批量验证失败: ${error.message}`, 'error');
            }
        }

        // 测试批量创建
        function testBatchCreation() {
            try {
                // 模拟批量创建过程
                const startTime = Date.now();
                
                setTimeout(() => {
                    const endTime = Date.now();
                    const duration = endTime - startTime;
                    
                    showResult('batchCreationResult', `✅ 模拟批量创建完成:\n处理时间: ${duration}ms\n成功: 3个订单\n失败: 0个订单`, 'success');
                }, 1000);
                
                showResult('batchCreationResult', '⏳ 正在模拟批量创建...', 'info');
            } catch (error) {
                showResult('batchCreationResult', `❌ 批量创建测试失败: ${error.message}`, 'error');
            }
        }

        // 测试大批量处理
        function testLargeBatch() {
            try {
                const startTime = Date.now();
                const batchSize = 100;
                
                // 模拟大批量数据处理
                const mockData = Array.from({ length: batchSize }, (_, i) => ({
                    id: i + 1,
                    processed: false
                }));
                
                // 分块处理
                const chunkSize = 10;
                let processed = 0;
                
                const processChunk = () => {
                    const chunk = mockData.slice(processed, processed + chunkSize);
                    chunk.forEach(item => item.processed = true);
                    processed += chunk.length;
                    
                    if (processed < batchSize) {
                        setTimeout(processChunk, 10);
                    } else {
                        const endTime = Date.now();
                        const duration = endTime - startTime;
                        showResult('largeBatchResult', `✅ 大批量处理完成:\n数据量: ${batchSize}\n处理时间: ${duration}ms\n平均速度: ${Math.round(batchSize / duration * 1000)}项/秒`, 'success');
                    }
                };
                
                processChunk();
                showResult('largeBatchResult', `⏳ 正在处理 ${batchSize} 项数据...`, 'info');
            } catch (error) {
                showResult('largeBatchResult', `❌ 大批量测试失败: ${error.message}`, 'error');
            }
        }

        // 测试内存使用
        function testMemoryUsage() {
            try {
                if (performance.memory) {
                    const memInfo = performance.memory;
                    const usedMB = Math.round(memInfo.usedJSHeapSize / 1048576);
                    const totalMB = Math.round(memInfo.totalJSHeapSize / 1048576);
                    const limitMB = Math.round(memInfo.jsHeapSizeLimit / 1048576);
                    
                    showResult('memoryResult', `✅ 内存使用情况:\n已使用: ${usedMB}MB\n总计: ${totalMB}MB\n限制: ${limitMB}MB\n使用率: ${Math.round(usedMB / limitMB * 100)}%`, 'success');
                } else {
                    showResult('memoryResult', '⚠️ 浏览器不支持内存监控API', 'info');
                }
            } catch (error) {
                showResult('memoryResult', `❌ 内存测试失败: ${error.message}`, 'error');
            }
        }

        // 测试错误恢复
        function testErrorRecovery() {
            try {
                // 模拟错误恢复机制
                let retryCount = 0;
                const maxRetries = 3;
                
                const simulateOperation = () => {
                    retryCount++;
                    
                    // 模拟前两次失败，第三次成功
                    if (retryCount < 3) {
                        setTimeout(() => {
                            showResult('errorRecoveryResult', `⚠️ 操作失败，正在重试... (${retryCount}/${maxRetries})`, 'info');
                            simulateOperation();
                        }, 500);
                    } else {
                        showResult('errorRecoveryResult', `✅ 错误恢复成功:\n重试次数: ${retryCount}\n最终状态: 成功`, 'success');
                    }
                };
                
                simulateOperation();
            } catch (error) {
                showResult('errorRecoveryResult', `❌ 错误恢复测试失败: ${error.message}`, 'error');
            }
        }

        // 运行所有测试
        function runAllTests() {
            // 重置统计
            testStats = { total: 0, passed: 0, failed: 0 };
            updateStats();
            
            // 依次运行所有测试
            testMultiDateDetection();
            setTimeout(testSeparatorDetection, 100);
            setTimeout(testOrderParsing, 200);
            setTimeout(testBatchValidation, 300);
            setTimeout(testBatchCreation, 400);
            setTimeout(testLargeBatch, 500);
            setTimeout(testMemoryUsage, 600);
            setTimeout(testErrorRecovery, 700);
        }

        // 清除所有结果
        function clearAllResults() {
            const results = document.querySelectorAll('.test-result');
            results.forEach(result => {
                result.classList.add('hidden');
                result.textContent = '';
            });
            
            testStats = { total: 0, passed: 0, failed: 0 };
            updateStats();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
        });
    </script>
</body>
</html>
