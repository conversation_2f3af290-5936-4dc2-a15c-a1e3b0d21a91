/**
 * UI管理器模块
 * 负责管理用户界面的交互、状态更新、事件处理等
 * 支持实时分析功能
 * 重构为传统script标签加载方式
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块（延迟获取以确保加载顺序）
    function getAppState() {
        return window.OTA.appState || window.appState;
    }

    function getLogger() {
        return window.OTA.logger || window.logger;
    }

    function getApiService() {
        return window.OTA.apiService || window.apiService;
    }

    // 在getGeminiService函数内新增代码，用于确保setRealtimeAnalysis存在
    function getGeminiService() {
        var service = window.OTA.geminiService || window.geminiService; // 获取全局Gemini服务实例
        if (service) {
            service.setRealtimeAnalysis = function(config) {
                // 如果传入值为 boolean，则调整为配置对象
                if (typeof config === 'boolean') {
                    this.configureRealtimeAnalysis({ enabled: config });
                } else {
                    this.configureRealtimeAnalysis(config);
                }
            };
        }
        return service;
    }

    function getUtils() {
        return window.OTA.utils || window.utils;
    }

class UIManager {
    constructor() {
        this.elements = {};
        this.isInitialized = false;
        this.currentModal = null;
        
        // 实时分析相关
        this.realtimeAnalysis = {
            enabled: true,
            debounceTimer: null,
            lastAnalysisTime: 0,
            isAnalyzing: false,
            progressIndicator: null
        };
        
        // 绑定方法上下文
        this.handleLogin = this.handleLogin.bind(this);
        this.handleLogout = this.handleLogout.bind(this);
        this.handleParseOrder = this.handleParseOrder.bind(this);
        this.handleCreateOrder = this.handleCreateOrder.bind(this);
        this.handleThemeToggle = this.handleThemeToggle.bind(this);
        this.handleRealtimeInput = this.handleRealtimeInput.bind(this);
    }
    
    /**
     * 初始化UI管理器
     */
    init() {
        this.cacheElements();
        this.bindEvents();
        this.setupStateListeners();
        this.initializeTheme();
        this.setupRealtimeAnalysis();
        this.loadSavedAccountInfo(); // 加载保存的账号信息
        this.updateUI();
        
        this.isInitialized = true;
        getLogger().log('UI管理器初始化完成', 'success');
    }
    
    /**
     * 缓存DOM元素
     */
    cacheElements() {
        this.elements = {
            // 主要容器
            loginPanel: document.getElementById('loginPanel'),
            workspace: document.getElementById('workspace'),
            
            // 登录相关
            loginForm: document.getElementById('loginForm'),
            emailInput: document.getElementById('email'),
            passwordInput: document.getElementById('password'),
            rememberMe: document.getElementById('rememberMe'),
            loginBtn: document.getElementById('loginBtn'),
            clearSavedBtn: document.getElementById('clearSavedBtn'),
            
            // 用户信息
            persistentEmailContainer: document.getElementById('persistentEmailContainer'),
            persistentEmail: document.getElementById('persistentEmail'),
            saveEmailBtn: document.getElementById('saveEmailBtn'),
            userInfo: document.getElementById('userInfo'),
            currentUser: document.getElementById('currentUser'),
            historyBtn: document.getElementById('historyBtn'),
            logoutBtn: document.getElementById('logoutBtn'),
            languageToggle: document.getElementById('languageToggle'),

            // 历史订单面板
            historyPanel: document.getElementById('historyPanel'),
            closeHistoryBtn: document.getElementById('closeHistoryBtn'),
            searchOrderId: document.getElementById('searchOrderId'),
            searchCustomer: document.getElementById('searchCustomer'),
            searchDateFrom: document.getElementById('searchDateFrom'),
            searchDateTo: document.getElementById('searchDateTo'),
            searchHistoryBtn: document.getElementById('searchHistoryBtn'),
            resetSearchBtn: document.getElementById('resetSearchBtn'),
            exportHistoryBtn: document.getElementById('exportHistoryBtn'),
            clearHistoryBtn: document.getElementById('clearHistoryBtn'),
            statTotal: document.getElementById('statTotal'),
            statToday: document.getElementById('statToday'),
            statWeek: document.getElementById('statWeek'),
            statMonth: document.getElementById('statMonth'),
            listCount: document.getElementById('listCount'),
            historyListContainer: document.getElementById('historyListContainer'),

            // 多订单面板
            multiOrderPanel: document.getElementById('multiOrderPanel'),
            closeMultiOrderBtn: document.getElementById('closeMultiOrderBtn'),
            multiOrderCount: document.getElementById('multiOrderCount'),
            multiOrderDateRange: document.getElementById('multiOrderDateRange'),
            multiOrderList: document.getElementById('multiOrderList'),
            batchCreateBtn: document.getElementById('batchCreateBtn'),
            selectAllOrdersBtn: document.getElementById('selectAllOrdersBtn'),
            deselectAllOrdersBtn: document.getElementById('deselectAllOrdersBtn'),
            validateAllOrdersBtn: document.getElementById('validateAllOrdersBtn'),
            selectedOrderCount: document.getElementById('selectedOrderCount'),
            createSelectedOrdersBtn: document.getElementById('createSelectedOrdersBtn'),
            
            // 智能输入
            orderInput: document.getElementById('orderInput'),
            parseBtn: document.getElementById('parseBtn'),
            clearInput: document.getElementById('clearInput'),
            sampleInput: document.getElementById('sampleInput'),
            geminiStatus: document.getElementById('geminiStatus'),
            
            // 订单表单
            orderForm: document.getElementById('orderForm'),
            createOrder: document.getElementById('createOrder'),
            previewOrder: document.getElementById('previewOrder'),
            validateOrder: document.getElementById('validateOrder'),
            resetOrder: document.getElementById('resetOrder'),
            
            // 表单字段
            subCategoryId: document.getElementById('subCategoryId'),
            otaReferenceNumber: document.getElementById('otaReferenceNumber'),
            otaChannel: document.getElementById('otaChannel'),
            otaChannelCustom: document.getElementById('otaChannelCustom'),
            carTypeId: document.getElementById('carTypeId'),
            inchargeByBackendUserId: document.getElementById('inchargeByBackendUserId'),
            customerName: document.getElementById('customerName'),
            customerContact: document.getElementById('customerContact'),
            customerEmail: document.getElementById('customerEmail'),
            flightInfo: document.getElementById('flightInfo'),
            pickup: document.getElementById('pickup'),
            destination: document.getElementById('destination'),
            date: document.getElementById('date'),
            time: document.getElementById('time'),
            passengerNumber: document.getElementById('passengerNumber'),
            luggageNumber: document.getElementById('luggageNumber'),
            drivingRegionId: document.getElementById('drivingRegionId'),
            languagesIdArray: document.getElementById('languagesIdArray'),
            tourGuide: document.getElementById('tourGuide'),
            babyChair: document.getElementById('babyChair'),
            meetAndGreet: document.getElementById('meetAndGreet'),
            otaPrice: document.getElementById('otaPrice'),
            driverFee: document.getElementById('driverFee'),
            driverCollect: document.getElementById('driverCollect'),
            extraRequirement: document.getElementById('extraRequirement'),
            
            // 状态栏
            connectionStatus: document.getElementById('connectionStatus'),
            dataStatus: document.getElementById('dataStatus'),
            lastUpdate: document.getElementById('lastUpdate'),
            
            // 主题切换
            themeToggle: document.getElementById('themeToggle'),
            
            // 模态框
            modal: document.getElementById('modal'),
            modalTitle: document.getElementById('modalTitle'),
            modalBody: document.getElementById('modalBody'),
            modalClose: document.getElementById('modalClose'),
            modalCancel: document.getElementById('modalCancel'),
            modalConfirm: document.getElementById('modalConfirm')
        };
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 登录表单
        this.elements.loginForm?.addEventListener('submit', this.handleLogin);
        this.elements.logoutBtn?.addEventListener('click', (e) => {
            console.log('登出按钮被点击');
            this.handleLogout();
        });
        this.elements.clearSavedBtn?.addEventListener('click', this.handleClearSaved.bind(this));

        // 持久化邮箱
        this.elements.persistentEmail?.addEventListener('input', () => this.validatePersistentEmail());
        this.elements.persistentEmail?.addEventListener('blur', () => this.savePersistentEmail());
        this.elements.saveEmailBtn?.addEventListener('click', () => this.savePersistentEmail());

        // 历史订单
        this.elements.historyBtn?.addEventListener('click', () => this.showHistoryPanel());
        this.elements.closeHistoryBtn?.addEventListener('click', () => this.hideHistoryPanel());
        this.elements.searchHistoryBtn?.addEventListener('click', () => this.searchHistory());
        this.elements.resetSearchBtn?.addEventListener('click', () => this.resetHistorySearch());
        this.elements.exportHistoryBtn?.addEventListener('click', () => this.exportHistory());
        this.elements.clearHistoryBtn?.addEventListener('click', () => this.clearHistory());

        // 多订单面板
        this.elements.closeMultiOrderBtn?.addEventListener('click', () => this.hideMultiOrderPanel());
        this.elements.batchCreateBtn?.addEventListener('click', () => this.handleBatchCreate());
        this.elements.selectAllOrdersBtn?.addEventListener('click', () => this.selectAllOrders());
        this.elements.deselectAllOrdersBtn?.addEventListener('click', () => this.deselectAllOrders());
        this.elements.validateAllOrdersBtn?.addEventListener('click', () => this.validateAllOrders());
        this.elements.createSelectedOrdersBtn?.addEventListener('click', () => this.createSelectedOrders());
        
        // 智能输入 - 实时分析
        this.elements.orderInput?.addEventListener('input', this.handleRealtimeInput);
        this.elements.orderInput?.addEventListener('paste', this.handleRealtimeInput);
        
        // 保留手动解析按钮作为备用
        this.elements.parseBtn?.addEventListener('click', this.handleParseOrder);
        this.elements.clearInput?.addEventListener('click', () => {
            this.elements.orderInput.value = '';
            this.clearRealtimeAnalysis();
            this.updateGeminiStatus('请输入订单描述');
        });
        this.elements.sampleInput?.addEventListener('click', () => {
            this.elements.orderInput.value = getGeminiService().generateSampleOrder();
            this.triggerRealtimeAnalysis();
            this.updateGeminiStatus('示例数据已填入，正在自动分析...');
        });
        
        // 订单表单
        this.elements.orderForm?.addEventListener('submit', this.handleCreateOrder);
        this.elements.previewOrder?.addEventListener('click', this.handlePreviewOrder.bind(this));
        this.elements.validateOrder?.addEventListener('click', this.handleValidateOrder.bind(this));
        this.elements.resetOrder?.addEventListener('click', this.handleResetOrder.bind(this));
        
        // 乘客人数变化时，提示用户重新解析以获得最佳车型推荐
        this.elements.passengerNumber?.addEventListener('change', () => {
            this.showQuickToast('乘客人数已变更，建议重新解析以获取最佳车型。', 'info');
        });
        
        // 子分类变化时，提示用户重新解析
        this.elements.subCategoryId?.addEventListener('change', () => {
            this.showQuickToast('服务类型已变更，建议重新解析以更新时间等信息。', 'info');
        });
        
        // 日志控制台
        // logConsole: document.getElementById('logConsole'),
        // clearLogs: document.getElementById('clearLogs'),
        // exportLogs: document.getElementById('exportLogs'),
        // debugMode: document.getElementById('debugMode'),
        
        // 状态栏
        // this.elements.connectionStatus?.addEventListener('change', this.handleConnectionStatusChange.bind(this));
        // this.elements.dataStatus?.addEventListener('change', this.handleDataStatusChange.bind(this));
        // this.elements.lastUpdate?.addEventListener('change', this.handleLastUpdateChange.bind(this));
        
        // 主题切换
        this.elements.themeToggle?.addEventListener('click', this.handleThemeToggle);

        // 语言切换
        this.elements.languageToggle?.addEventListener('click', () => this.handleLanguageToggle());
        
        // 模态框
        this.elements.modalClose?.addEventListener('click', this.hideModal.bind(this));
        this.elements.modalCancel?.addEventListener('click', this.hideModal.bind(this));
        this.elements.modal?.addEventListener('click', (e) => {
            if (e.target === this.elements.modal) {
                this.hideModal();
            }
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
        
        // 实时输入验证
        this.setupRealtimeValidation();
        
        // 调试按钮已移除

        // 预览浮窗关闭按钮
        const closePreviewModal = document.getElementById('closePreviewModal');
        if (closePreviewModal) {
            closePreviewModal.addEventListener('click', () => this.hidePreviewModal());
        }

        // 预览浮窗遮罩点击关闭
        const previewModalOverlay = document.getElementById('previewModalOverlay');
        if (previewModalOverlay) {
            previewModalOverlay.addEventListener('click', (e) => {
                if (e.target === previewModalOverlay) {
                    this.hidePreviewModal();
                }
            });
        }

        // 编辑字段按钮事件委托
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('edit-field-btn')) {
                this.handleFieldEdit(e.target);
            }
        });

        // 初始化国际化
        this.initializeI18n();
    }
    
    /**
     * 设置实时分析功能
     */
    setupRealtimeAnalysis() {
        // 配置实时分析参数
        getGeminiService().configureRealtimeAnalysis({
            debounceDelay: 2000, // 2秒防抖
            minInputLength: 15,  // 最小15字符
            confidenceThreshold: 0.2
        });
        
        // 启用实时分析
        const gsInstance = getGeminiService();
        gsInstance.setRealtimeAnalysis(true);
        
        // 创建进度指示器
        this.createProgressIndicator();
        
        getLogger().log('实时分析功能已设置', 'info');
    }
    
    /**
     * 创建进度指示器
     */
    createProgressIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'realtime-progress';
        indicator.innerHTML = `
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            <div class="progress-text">正在分析...</div>
        `;
        
        indicator.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-top: none;
            border-radius: 0 0 var(--radius-md) var(--radius-md);
            padding: var(--spacing-3);
            display: none;
            z-index: 10;
        `;
        
        const progressBarStyle = `
            .realtime-progress .progress-bar {
                width: 100%;
                height: 4px;
                background: var(--color-gray-200);
                border-radius: 2px;
                overflow: hidden;
                margin-bottom: var(--spacing-2);
            }
            .realtime-progress .progress-fill {
                height: 100%;
                background: var(--color-primary);
                border-radius: 2px;
                transition: width 0.3s ease;
                width: 0%;
            }
            .realtime-progress .progress-text {
                font-size: var(--font-size-xs);
                color: var(--text-secondary);
                text-align: center;
            }
        `;
        
        // 添加样式
        if (!document.getElementById('realtime-progress-styles')) {
            const style = document.createElement('style');
            style.id = 'realtime-progress-styles';
            style.textContent = progressBarStyle;
            document.head.appendChild(style);
        }
        
        // 将指示器添加到输入框容器
        const inputContainer = this.elements.orderInput?.parentElement;
        if (inputContainer) {
            inputContainer.style.position = 'relative';
            inputContainer.appendChild(indicator);
            this.realtimeAnalysis.progressIndicator = indicator;
        }
    }
    
    /**
     * 处理实时输入
     * @param {Event} event - 输入事件
     */
    handleRealtimeInput(event) {
        const inputText = event.target.value;
        
        // 清除之前的定时器
        if (this.realtimeAnalysis.debounceTimer) {
            clearTimeout(this.realtimeAnalysis.debounceTimer);
        }
        
        // 如果输入为空，清除分析状态
        if (!inputText.trim()) {
            this.clearRealtimeAnalysis();
            this.updateGeminiStatus('请输入订单描述');
            return;
        }
        
        // 检查输入长度
        if (inputText.trim().length < 15) {
            this.updateGeminiStatus(`请继续输入... (${inputText.trim().length}/15)`);
            return;
        }
        
        // 设置防抖定时器
        this.realtimeAnalysis.debounceTimer = setTimeout(() => {
            this.triggerRealtimeAnalysis();
        }, 2000);
        
        // 更新状态提示
        this.updateGeminiStatus('输入检测中，即将开始分析...');
    }
    
    /**
     * 触发实时分析
     */
    async triggerRealtimeAnalysis() {
        const orderText = this.elements.orderInput?.value?.trim();
        
        if (!orderText || !getGeminiService().isAvailable()) {
            return;
        }
        
        // 防止重复分析
        if (this.realtimeAnalysis.isAnalyzing) {
            return;
        }
        
        this.realtimeAnalysis.isAnalyzing = true;
        this.realtimeAnalysis.lastAnalysisTime = Date.now();
        
        // 显示进度指示器
        this.showProgressIndicator();
        
        try {
            // 使用 parseOrder 方法进行实时分析
            const result = await getGeminiService().parseOrder(orderText, true);
            
            if (result && Array.isArray(result) && result.length > 0) {
                // 模拟 analyzeRealtime 的返回格式
                const orderData = result[0];
                const confidence = this.calculateDataConfidence(orderData) / 100;
                
                this.handleAnalysisResult({
                    success: true,
                    data: orderData,
                    confidence: confidence
                });
            } else {
                this.handleAnalysisResult({
                    success: false,
                    error: '无法解析订单内容',
                    confidence: 0
                });
            }
        } catch (error) {
            this.handleAnalysisError(error);
        }
    }
    
    /**
     * 处理分析进度
     * @param {string} message - 进度消息
     * @param {number} progress - 进度百分比
     */
    handleAnalysisProgress(message, progress) {
        this.updateGeminiStatus(message);
        this.updateProgressIndicator(progress);
    }
    
    /**
     * 处理分析结果
     * @param {object} result - 分析结果
     */
    handleAnalysisResult(result) {
        this.realtimeAnalysis.isAnalyzing = false;
        this.hideProgressIndicator();
        
        if (result.success && result.data) {
            // 更新应用状态
            getAppState().setCurrentOrder({
                rawInput: this.elements.orderInput.value,
                parsedData: result.data,
                formData: result.data,
                status: 'parsed'
            });

            // 移除：直接填充表单，这可能导致竞态条件
            // this.fillFormFromData(result.data);
            // 现在将由 AppState 的 'currentOrder' 监听器来触发 UI 更新，
            // 该监听器 (updateOrderForm) 会确保在填充前加载好下拉列表数据。

            const confidence = Math.round((result.confidence || 0) * 100);
            this.updateGeminiStatus(`✅ 自动解析完成！置信度: ${confidence}%`);

            // 显示简短的成功提示
            this.showQuickToast(`AI自动解析成功 (${confidence}%)`, 'success');

            // 自动填充客户邮箱（如果需要）
            this.autoFillCustomerEmail();

            // 处理价格转换
            if (result.data) {
                this.processPriceConversion(result.data);
            }

            // 处理多订单模式
            const orderText = this.elements.orderInput?.value || '';
            if (result.data && orderText) {
                this.processMultiOrderMode(result.data, orderText);
            }

            // 处理举牌服务
            if (result.data && orderText) {
                this.processPagingService(result.data, orderText);
            }

            // 根据订单模式显示相应的预览界面
            if (this.isCurrentlyMultiOrderMode()) {
                this.showMultiOrderPanel();
            } else {
                this.showPreviewModal();
            }

        } else if (result.fallback && Object.keys(result.data).length > 0) {
            // 降级解析
            // 移除直接填充，改为通过更新状态来触发UI更新
            // this.fillFormFromData(result.data);

            // 与成功路径保持一致，通过设置状态来驱动UI更新
            getAppState().setCurrentOrder({
                rawInput: this.elements.orderInput.value,
                parsedData: result.data,
                formData: result.data,
                status: 'parsed_fallback'
            });

            this.updateGeminiStatus('⚠️ 使用基础解析模式');
            this.showQuickToast('使用基础解析模式', 'warning');

            // 显示预览浮窗
            this.showPreviewModal();
        } else {
            this.updateGeminiStatus(`❌ 解析失败: ${result.error}`);
        }
        
        getLogger().logGeminiInteraction(
            this.elements.orderInput.value,
            result,
            result.confidence
        );
    }
    
    /**
     * 处理分析错误
     * @param {Error} error - 错误对象
     */
    handleAnalysisError(error) {
        this.realtimeAnalysis.isAnalyzing = false;
        this.hideProgressIndicator();
        
        this.updateGeminiStatus(`❌ 分析错误: ${error.message}`);
        getLogger().logError('实时分析失败', error);
    }
    
    /**
     * 显示进度指示器
     */
    showProgressIndicator() {
        if (this.realtimeAnalysis.progressIndicator) {
            this.realtimeAnalysis.progressIndicator.style.display = 'block';
        }
    }
    
    /**
     * 隐藏进度指示器
     */
    hideProgressIndicator() {
        if (this.realtimeAnalysis.progressIndicator) {
            this.realtimeAnalysis.progressIndicator.style.display = 'none';
        }
    }
    
    /**
     * 更新进度指示器
     * @param {number} progress - 进度百分比
     */
    updateProgressIndicator(progress) {
        if (this.realtimeAnalysis.progressIndicator) {
            const progressFill = this.realtimeAnalysis.progressIndicator.querySelector('.progress-fill');
            if (progressFill) {
                progressFill.style.width = `${Math.min(progress, 100)}%`;
            }
        }
    }
    
    /**
     * 清除实时分析状态
     */
    clearRealtimeAnalysis() {
        // 清除定时器
        if (this.realtimeAnalysis.debounceTimer) {
            clearTimeout(this.realtimeAnalysis.debounceTimer);
            this.realtimeAnalysis.debounceTimer = null;
        }
        
        // 取消当前分析
        getGeminiService().cancelCurrentAnalysis();
        
        // 重置状态
        this.realtimeAnalysis.isAnalyzing = false;
        this.hideProgressIndicator();
    }
    
    /**
     * 显示预览浮窗
     */
    showPreviewModal() {
        const previewSection = document.getElementById('previewSection');
        const inputSection = document.querySelector('.input-section');

        if (previewSection && inputSection) {
            // 计算智能输入区的位置
            const inputRect = inputSection.getBoundingClientRect();
            const inputBottom = inputRect.bottom;

            // 设置浮窗位置
            const modalContent = previewSection.querySelector('.preview-modal-content');
            if (modalContent) {
                // 计算距离顶部的距离（智能输入区底部 + 小间距）
                const topOffset = inputBottom + 10;
                modalContent.style.marginTop = `${topOffset}px`;
                modalContent.style.marginBottom = 'auto';
            }

            previewSection.classList.add('show-preview');
            previewSection.style.display = 'block';

            // 防止背景滚动
            document.body.style.overflow = 'hidden';

            getLogger().log('预览浮窗已显示，定位在智能输入区下方', 'info');
        }
    }

    /**
     * 隐藏预览浮窗
     */
    hidePreviewModal() {
        const previewSection = document.getElementById('previewSection');
        if (previewSection) {
            previewSection.classList.remove('show-preview');
            previewSection.style.display = 'none';

            // 恢复背景滚动
            document.body.style.overflow = '';

            getLogger().log('预览浮窗已隐藏', 'info');
        }
    }

    /**
     * 处理字段编辑
     * @param {HTMLElement} button - 编辑按钮
     */
    handleFieldEdit(button) {
        const fieldName = button.getAttribute('data-field');
        const fieldGroup = button.closest('.editable-field');
        const field = document.getElementById(fieldName);

        if (!field || !fieldGroup) return;

        // 切换编辑状态
        if (fieldGroup.classList.contains('editing')) {
            // 保存编辑
            this.saveFieldEdit(fieldGroup, field);
        } else {
            // 开始编辑
            this.startFieldEdit(fieldGroup, field);
        }
    }

    /**
     * 开始字段编辑
     * @param {HTMLElement} fieldGroup - 字段组容器
     * @param {HTMLElement} field - 字段元素
     */
    startFieldEdit(fieldGroup, field) {
        fieldGroup.classList.add('editing');
        field.focus();

        // 存储原始值
        fieldGroup.setAttribute('data-original-value', field.value);

        getLogger().log(`开始编辑字段: ${field.id}`, 'info');
    }

    /**
     * 保存字段编辑
     * @param {HTMLElement} fieldGroup - 字段组容器
     * @param {HTMLElement} field - 字段元素
     */
    saveFieldEdit(fieldGroup, field) {
        fieldGroup.classList.remove('editing');

        // 移除原始值存储
        fieldGroup.removeAttribute('data-original-value');

        // 显示保存成功提示
        this.showQuickToast(`字段 ${field.id} 已保存`, 'success');

        getLogger().log(`字段编辑已保存: ${field.id} = ${field.value}`, 'success');
    }

    /**
     * 显示简洁的订单创建成功提示
     * @param {string} orderId - 订单号
     */
    showSimpleSuccessToast(orderId) {
        // 创建成功提示元素
        const toast = document.createElement('div');
        toast.className = 'order-success-toast';
        toast.innerHTML = `
            <div class="success-content">
                <div class="success-icon">✅</div>
                <div class="success-text">
                    <div class="success-title">订单创建成功！</div>
                    <div class="success-order-id">订单号：<span class="order-id">${orderId}</span></div>
                </div>
                <button class="copy-order-btn" title="复制订单号">📋</button>
            </div>
        `;

        // 设置样式
        toast.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 2px solid var(--color-success);
            border-radius: var(--radius-lg);
            padding: var(--spacing-4);
            box-shadow: var(--shadow-lg);
            z-index: 3000;
            min-width: 300px;
            text-align: center;
            animation: successToastIn 0.3s ease-out;
        `;

        // 添加内部样式
        const style = document.createElement('style');
        style.textContent = `
            .order-success-toast .success-content {
                display: flex;
                align-items: center;
                gap: var(--spacing-3);
            }
            .order-success-toast .success-icon {
                font-size: 2rem;
                flex-shrink: 0;
            }
            .order-success-toast .success-text {
                flex: 1;
                text-align: left;
            }
            .order-success-toast .success-title {
                font-weight: 600;
                color: var(--color-success);
                margin-bottom: var(--spacing-1);
            }
            .order-success-toast .success-order-id {
                font-size: var(--font-size-sm);
                color: var(--text-secondary);
            }
            .order-success-toast .order-id {
                font-family: monospace;
                font-weight: 600;
                color: var(--color-primary);
            }
            .order-success-toast .copy-order-btn {
                background: transparent;
                border: 1px solid var(--border-color);
                border-radius: var(--radius-sm);
                padding: var(--spacing-1);
                cursor: pointer;
                font-size: 1.2rem;
                transition: all var(--transition-fast);
                flex-shrink: 0;
            }
            .order-success-toast .copy-order-btn:hover {
                background: var(--color-primary-light);
                border-color: var(--color-primary);
            }
            @keyframes successToastIn {
                from {
                    opacity: 0;
                    transform: translate(-50%, -50%) scale(0.8);
                }
                to {
                    opacity: 1;
                    transform: translate(-50%, -50%) scale(1);
                }
            }
            @keyframes successToastOut {
                from {
                    opacity: 1;
                    transform: translate(-50%, -50%) scale(1);
                }
                to {
                    opacity: 0;
                    transform: translate(-50%, -50%) scale(0.8);
                }
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(toast);

        // 复制订单号功能
        const copyBtn = toast.querySelector('.copy-order-btn');
        copyBtn.addEventListener('click', () => {
            navigator.clipboard.writeText(orderId).then(() => {
                this.showQuickToast(`订单号 ${orderId} 已复制`, 'success');
            });
        });

        // 3秒后自动关闭
        setTimeout(() => {
            toast.style.animation = 'successToastOut 0.3s ease-in';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
                if (style.parentNode) {
                    style.parentNode.removeChild(style);
                }
            }, 300);
        }, 3000);

        getLogger().log(`订单创建成功提示已显示，订单号: ${orderId}`, 'success');
    }

    /**
     * 显示快速提示
     * @param {string} message - 提示消息
     * @param {string} type - 提示类型
     */
    showQuickToast(message, type = 'info') {
        // 创建简单的提示元素
        const toast = document.createElement('div');
        toast.className = `quick-toast ${type}`;
        toast.textContent = message;
        
        toast.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: var(--color-${type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info'});
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(toast);
        
        // 显示动画
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 10);
        
        // 自动隐藏
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 2000);
    }
    
    /**
     * 设置状态监听器
     */
    setupStateListeners() {
        // 监听登录状态变化
        getAppState().on('auth.isLoggedIn', (isLoggedIn) => {
            this.updateLoginUI(isLoggedIn);
        });
        
        // 监听系统数据变化
        getAppState().on('systemData.lastUpdated', () => {
            this.populateFormOptions();
            this.updateDataStatus();
        });
        
        // 监听当前订单变化
        getAppState().on('currentOrder', () => {
            this.updateOrderForm();
        });
        
        // 监听主题变化
        getAppState().on('config.theme', (theme) => {
            document.documentElement.setAttribute('data-theme', theme);
            this.updateThemeIcon(theme);
        });
        
        // 监听连接状态变化
        getAppState().on('system.connected', (connected) => {
            this.updateConnectionStatus(connected);
        });
    }
    
    /**
     * 初始化主题
     */
    initializeTheme() {
        const theme = getAppState().get('config.theme') || 'light';
        document.documentElement.setAttribute('data-theme', theme);
        this.updateThemeIcon(theme);
    }
    
    /**
     * 更新UI状态
     */
    updateUI() {
        const isLoggedIn = getAppState().get('auth.isLoggedIn');
        this.updateLoginUI(isLoggedIn);
        
        if (isLoggedIn) {
            this.populateFormOptions();
            this.updateDataStatus();
        }
        
        this.updateConnectionStatus(getAppState().get('system.connected'));
        this.updateLastUpdateTime();
        this.updateGeminiStatus();
    }
    
    /**
     * 处理登录
     * @param {Event} e - 表单提交事件
     */
    async handleLogin(e) {
        e.preventDefault();
        
        const email = this.elements.emailInput.value.trim();
        const password = this.elements.passwordInput.value;
        const rememberMe = this.elements.rememberMe.checked;
        
        if (!email || !password) {
            this.showAlert('请输入邮箱和密码', 'warning');
            return;
        }
        
        this.setButtonLoading(this.elements.loginBtn, true);
        
        try {
            await getApiService().login(email, password, rememberMe);
            
            // 保存账号信息（如果用户选择记住登录状态）
            this.saveAccountInfo(email, rememberMe);
            
            // 登录成功后获取系统数据
            try {
                await getApiService().getAllSystemData();
                // 重新填充表单选项
                this.populateFormOptions();
                // 自动填充OTA渠道（登录成功后立即执行）
                this.populateOtaChannelOptions();
                this.showAlert('登录成功！', 'success');
                getLogger().log('登录成功，系统数据已更新', 'success');
            } catch (error) {
                getLogger().log('获取系统数据失败，使用静态数据', 'warning', { error: error.message });
                // 即使系统数据获取失败，也要尝试填充OTA渠道
                this.populateOtaChannelOptions();
                this.showAlert('登录成功，但系统数据获取失败，将使用本地数据', 'warning');
            }
            
        } catch (error) {
            this.showAlert(`登录失败: ${error.message}`, 'error');
        } finally {
            this.setButtonLoading(this.elements.loginBtn, false);
        }
    }
    
    /**
     * 处理登出
     */
    handleLogout() {
        console.log('handleLogout 方法被调用');

        try {
            this.showConfirm(
                '确认登出',
                '确定要退出登录吗？当前未保存的数据可能丢失。',
                () => {
                    console.log('用户确认登出，开始执行登出逻辑');

                    try {
                        // 检查是否保持账号信息
                        const rememberMe = getAppState().get('auth.rememberMe');
                        console.log('rememberMe状态:', rememberMe);

                        console.log('清除认证状态...');
                        getAppState().clearAuth(rememberMe);

                        console.log('清除当前订单...');
                        getAppState().clearCurrentOrder();

                        console.log('清除实时分析...');
                        this.clearRealtimeAnalysis();

                        // 如果没有选择记住登录状态，清除保存的账号信息
                        if (!rememberMe) {
                            console.log('清除保存的账号信息...');
                            this.clearSavedAccountInfo();
                        } else {
                            // 如果选择了记住登录状态，只清除密码，保留邮箱
                            console.log('保留邮箱，清除密码...');
                            if (this.elements.passwordInput) {
                                this.elements.passwordInput.value = '';
                            }
                        }

                        // 手动更新UI以确保立即显示登录界面
                        console.log('更新UI状态...');
                        console.log('loginPanel元素:', this.elements.loginPanel);
                        console.log('workspace元素:', this.elements.workspace);
                        console.log('userInfo元素:', this.elements.userInfo);

                        this.updateLoginUI(false);

                        console.log('登出后UI状态:');
                        console.log('loginPanel display:', this.elements.loginPanel?.style.display);
                        console.log('workspace display:', this.elements.workspace?.style.display);
                        console.log('userInfo display:', this.elements.userInfo?.style.display);

                        this.showAlert('已退出登录', 'info');
                        console.log('登出流程完成');

                    } catch (error) {
                        console.error('登出过程中发生错误:', error);
                        this.showAlert('登出过程中发生错误: ' + error.message, 'error');
                    }
            }
        );
        } catch (error) {
            console.error('显示确认对话框时发生错误:', error);
            this.showAlert('无法显示确认对话框: ' + error.message, 'error');
        }
    }
    
    /**
     * 处理手动订单解析（保留作为备用功能）
     */
    async handleParseOrder() {
        const orderText = this.elements.orderInput.value.trim();
        
        if (!orderText) {
            this.showAlert('请输入订单描述', 'warning');
            return;
        }
        
        if (!getGeminiService().isAvailable()) {
            this.showApiKeyPrompt();
            return;
        }
        
        // 停止实时分析
        this.clearRealtimeAnalysis();
        
        this.setButtonLoading(this.elements.parseBtn, true);
        this.updateGeminiStatus('手动解析中...');
        
        try {
            const result = await getGeminiService().parseOrder(orderText);
            
            if (result && Array.isArray(result) && result.length > 0) {
                // parseOrder 返回的是数组，取第一个订单数据
                const orderData = result[0];
                
                // 更新应用状态
                getAppState().setCurrentOrder({
                    rawInput: orderText,
                    parsedData: orderData,
                    formData: orderData,
                    status: 'parsed'
                });
                
                // 填充表单
                this.fillFormFromData(orderData);
                
                // 计算置信度（基于非空字段数量）
                const confidence = this.calculateDataConfidence(orderData);
                this.updateGeminiStatus(`手动解析完成！置信度: ${confidence}%`);
                this.showAlert(`手动解析成功，置信度: ${confidence}%`, 'success');

                // 显示预览浮窗
                this.showPreviewModal();
                
            } else {
                this.updateGeminiStatus('解析失败: 无法解析订单内容');
                this.showAlert('解析失败: 无法解析订单内容', 'error');
            }
            
        } catch (error) {
            this.updateGeminiStatus(`解析错误: ${error.message}`);
            this.showAlert(`解析错误: ${error.message}`, 'error');
        } finally {
            this.setButtonLoading(this.elements.parseBtn, false);
        }
    }
    
    /**
     * 处理订单创建
     * @param {Event} e - 表单提交事件
     */
    async handleCreateOrder(e) {
        e.preventDefault();
        
        const orderData = this.collectFormData();
        const validation = getApiService().validateOrderData(orderData);
        
        if (!validation.isValid) {
            this.showValidationErrors(validation.errors);
            return;
        }
        
        if (validation.warnings.length > 0) {
            this.showAlert(`警告: ${validation.warnings.join(', ')}`, 'warning');
        }
        
        this.setButtonLoading(this.elements.createOrder, true);
        
        try {
            const result = await getApiService().createOrder(orderData);
            // 构建详细返回内容
            const detailHtml = `
                <div class="api-result-block">
                    <h5>API返回内容</h5>
                    <pre class="api-result-json" style="background:#222;color:#fff;padding:8px;border-radius:4px;max-height:300px;overflow:auto;">${JSON.stringify(result, null, 2)}</pre>
                    <button id="copyApiResultBtn" class="btn btn-outline btn-sm" style="margin-top:8px;">复制返回内容</button>
                </div>
            `;
            if (result.success) {
                // 提取订单ID
                const orderId = result.data?.id || result.data?.order_id || result.id || 'N/A';

                // 保存到历史记录
                try {
                    const orderData = this.collectFormData();
                    getOrderHistoryManager().addOrder(orderData, orderId, result);
                    getLogger().log(`订单已保存到历史记录: ${orderId}`, 'success');
                } catch (error) {
                    getLogger().log(`保存历史记录失败: ${error.message}`, 'error');
                }

                // 显示简洁的成功提示
                this.showSimpleSuccessToast(orderId);

                // 自动关闭预览浮窗
                this.hidePreviewModal();

                // 自动重置表单（无确认对话框）
                setTimeout(() => {
                    this.resetOrderFormDirectly();
                }, 3000); // 3秒后自动重置表单
            } else {
                this.showValidationErrors(result.errors);
                this.showAlert(`订单创建失败: ${result.message}`, 'error');
                this.showModal('订单创建失败', `
                    <div class="error-message">
                        <p><strong>订单创建失败</strong></p>
                        <p>错误信息: ${result.message || '无'}</p>
                    </div>
                    ${detailHtml}
                `);
                setTimeout(() => {
                    const btn = document.getElementById('copyApiResultBtn');
                    if (btn) {
                        btn.onclick = () => {
                            navigator.clipboard.writeText(JSON.stringify(result, null, 2));
                            this.showAlert('API返回内容已复制', 'success', 2000);
                        };
                    }
                }, 300);
            }
        } catch (error) {
            // 捕获异常也展示详细内容
            const errorDetail = {
                message: error.message,
                stack: error.stack
            };
            const detailHtml = `
                <div class="api-result-block">
                    <h5>API异常信息</h5>
                    <pre class="api-result-json" style="background:#222;color:#fff;padding:8px;border-radius:4px;max-height:300px;overflow:auto;">${JSON.stringify(errorDetail, null, 2)}</pre>
                    <button id="copyApiResultBtn" class="btn btn-outline btn-sm" style="margin-top:8px;">复制异常内容</button>
                </div>
            `;
            this.showAlert(`订单创建异常: ${error.message}`, 'error');
            this.showModal('订单创建异常', detailHtml);
            setTimeout(() => {
                const btn = document.getElementById('copyApiResultBtn');
                if (btn) {
                    btn.onclick = () => {
                        navigator.clipboard.writeText(JSON.stringify(errorDetail, null, 2));
                        this.showAlert('异常内容已复制', 'success', 2000);
                    };
                }
            }, 300);
        } finally {
            this.setButtonLoading(this.elements.createOrder, false);
        }
    }
    
    /**
     * 处理订单预览 - 优化为紧凑多列布局
     */
    handlePreviewOrder() {
        const orderData = this.collectFormData();
        const validation = getApiService().validateOrderData(orderData);
        
        let content = '<div class="order-preview">';
        content += '<h4>订单预览</h4>';
        
        // 使用多列网格布局
        content += '<div class="preview-grid">';
        
        // 基本信息
        content += '<div class="preview-section">';
        content += '<h5>📋 基本信息</h5>';
        content += `<p><strong>子分类:</strong> ${this.getSubCategoryName(orderData.sub_category_id)}</p>`;
        content += `<p><strong>OTA号:</strong> ${orderData.ota_reference_number || 'N/A'}</p>`;
        if (orderData.ota) content += `<p><strong>OTA渠道:</strong> ${orderData.ota}</p>`;
        content += `<p><strong>车型:</strong> ${this.getCarTypeName(orderData.car_type_id)}</p>`;
        if (orderData.driving_region_id) content += `<p><strong>区域:</strong> ${this.getDrivingRegionName(orderData.driving_region_id)}</p>`;
        const languages = this.getLanguageNames(orderData.languages_id_array);
        if (languages !== 'N/A') content += `<p><strong>语言:</strong> ${languages}</p>`;
        content += '</div>';
        
        // 客户信息
            content += '<div class="preview-section">';
        content += '<h5>👤 客户信息</h5>';
            if (orderData.customer_name) content += `<p><strong>姓名:</strong> ${orderData.customer_name}</p>`;
            if (orderData.customer_contact) content += `<p><strong>电话:</strong> ${orderData.customer_contact}</p>`;
            if (orderData.customer_email) content += `<p><strong>邮箱:</strong> ${orderData.customer_email}</p>`;
        if (orderData.flight_info) content += `<p><strong>航班:</strong> ${orderData.flight_info}</p>`;
        if (!orderData.customer_name && !orderData.customer_contact && !orderData.customer_email && !orderData.flight_info) {
            content += '<p><strong>暂无客户信息</strong></p>';
        }
        content += '</div>';
        
        // 行程信息
        content += '<div class="preview-section">';
        content += '<h5>🚗 行程信息</h5>';
        content += `<p><strong>上车:</strong> ${orderData.pickup || 'N/A'}</p>`;
        content += `<p><strong>目的地:</strong> ${orderData.destination || 'N/A'}</p>`;
        const previewDate = orderData.date ? getUtils().formatDate(orderData.date, 'YYYY-MM-DD') : 'N/A';
        content += `<p><strong>日期:</strong> ${previewDate}</p>`;
        content += `<p><strong>时间:</strong> ${orderData.time || 'N/A'}</p>`;
        if (orderData.passenger_number) content += `<p><strong>乘客:</strong> ${orderData.passenger_number}人</p>`;
        if (orderData.luggage_number) content += `<p><strong>行李:</strong> ${orderData.luggage_number}件</p>`;
        content += '</div>';

        // 费用信息
        const hasPrice = orderData.ota_price || orderData.driver_fee || orderData.driver_collect;
        if (hasPrice) {
        content += '<div class="preview-section">';
            content += '<h5>💰 费用信息</h5>';
        if (orderData.ota_price) content += `<p><strong>OTA价格:</strong> RM ${orderData.ota_price}</p>`;
            if (orderData.driver_fee) content += `<p><strong>司机费:</strong> RM ${orderData.driver_fee}</p>`;
        if (orderData.driver_collect) content += `<p><strong>司机收取:</strong> RM ${orderData.driver_collect}</p>`;
            content += '</div>';
        }

        // 特殊服务
        const specialServices = [];
        if (orderData.tour_guide) specialServices.push('导游服务');
        if (orderData.baby_chair) specialServices.push('儿童座椅');
        if (orderData.meet_and_greet) specialServices.push('接机服务');
        
        const hasSpecialService = specialServices.length > 0 || orderData.extra_requirement;
        if (hasSpecialService) {
            content += '<div class="preview-section">';
            content += '<h5>⭐ 特殊服务</h5>';
        if (specialServices.length > 0) {
                content += `<p><strong>服务:</strong> ${specialServices.join(', ')}</p>`;
        }
            if (orderData.extra_requirement) {
                content += `<p><strong>要求:</strong> ${orderData.extra_requirement}</p>`;
            }
        content += '</div>';
        }
        
        // 验证结果
        if (!validation.isValid || validation.warnings.length > 0) {
            content += '<div class="preview-section">';
            content += '<h5>⚠️ 验证结果</h5>';
            
        if (!validation.isValid) {
                content += '<div style="color: var(--color-error); margin-bottom: var(--spacing-2);">';
                content += '<strong>错误:</strong><br>';
            Object.entries(validation.errors).forEach(([field, errors]) => {
                    content += `• ${field}: ${errors.join(', ')}<br>`;
            });
            content += '</div>';
        }
        
        if (validation.warnings.length > 0) {
                content += '<div style="color: var(--color-warning);">';
                content += '<strong>警告:</strong><br>';
            validation.warnings.forEach(warning => {
                    content += `• ${warning}<br>`;
            });
            content += '</div>';
        }
        content += '</div>';
        }
        
        content += '</div>'; // 结束 preview-grid
        content += '</div>'; // 结束 order-preview
        
        this.showModal('订单预览', content);
    }
    
    /**
     * 处理订单验证
     */
    handleValidateOrder() {
        const orderData = this.collectFormData();
        const validation = getApiService().validateOrderData(orderData);
        
        if (validation.isValid) {
            this.showAlert('数据验证通过！', 'success');
        } else {
            this.showValidationErrors(validation.errors);
        }
        
        if (validation.warnings.length > 0) {
            this.showAlert(`警告: ${validation.warnings.join(', ')}`, 'warning');
        }
    }
    
    /**
     * 处理订单重置（带确认对话框）
     */
    handleResetOrder() {
        this.showConfirm(
            '重置表单',
            '确定要重置所有表单数据吗？',
            () => {
                this.resetOrderFormDirectly();
            }
        );
    }

    /**
     * 直接重置订单表单（无确认对话框）
     * 用于订单创建成功后的自动重置
     */
    resetOrderFormDirectly() {
        // 重置表单
        this.elements.orderForm.reset();
        this.elements.orderInput.value = '';

        // 清除状态
        this.clearRealtimeAnalysis();
        getAppState().clearCurrentOrder();

        // 更新UI状态
        this.updateGeminiStatus('请输入订单描述');

        getLogger().log('表单已自动重置', 'info');
    }
    
    /**
     * 处理主题切换
     */
    handleThemeToggle() {
        const currentTheme = getAppState().get('config.theme');
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        getAppState().setTheme(newTheme);
    }
    
    /**
     * 处理清空日志
     */
    handleClearLogs() {
        getLogger().clear();
        // this.updateLogConsole();
    }
    
    /**
     * 处理导出日志
     */
    handleExportLogs() {
        const logs = getLogger().export();
        const blob = new Blob([JSON.stringify(logs, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `ota-system-logs-${getUtils().formatDate(new Date())}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showAlert('日志已导出', 'success');
    }
    
    /**
     * 处理调试模式变化
     */
    handleDebugModeChange() {
        const enabled = this.elements.debugMode.checked;
        getAppState().setDebugMode(enabled);
        getLogger().setDebugMode(enabled);
        
        this.showAlert(`调试模式已${enabled ? '启用' : '禁用'}`, 'info');
    }
    
    /**
     * 处理键盘快捷键
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + Enter: 手动解析订单
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            if (document.activeElement === this.elements.orderInput) {
                e.preventDefault();
                this.handleParseOrder();
            }
        }
        
        // Escape: 关闭模态框
        if (e.key === 'Escape' && this.currentModal) {
            this.hideModal();
        }
        
        // Ctrl/Cmd + Shift + R: 重置表单
        if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'R') {
            e.preventDefault();
            this.handleResetOrder();
        }
    }
    
    /**
     * 设置实时输入验证
     */
    setupRealtimeValidation() {
        // 邮箱验证
        if (this.elements.customerEmail) {
            this.elements.customerEmail.addEventListener('blur', () => {
                const email = this.elements.customerEmail.value;
                if (email && !getApiService().isValidEmail(email)) {
                    this.showFieldError('customerEmail', '邮箱格式不正确');
                } else {
                    this.clearFieldError('customerEmail');
                }
            });
        }
        
        // 电话验证
        if (this.elements.customerContact) {
            this.elements.customerContact.addEventListener('blur', () => {
                const phone = this.elements.customerContact.value;
                if (phone && !getApiService().isValidPhone(phone)) {
                    this.showFieldError('customerContact', '电话格式可能不正确');
                } else {
                    this.clearFieldError('customerContact');
                }
            });
        }
        
        // 日期验证
        if (this.elements.date) {
            this.elements.date.addEventListener('change', () => {
                const date = this.elements.date.value;
                if (date) {
                    const selectedDate = new Date(date);
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);
                    
                    if (selectedDate < today) {
                        this.showFieldError('date', '不能选择过去的日期');
                    } else {
                        this.clearFieldError('date');
                    }
                }
            });
        }
    }
    
    /**
     * 更新登录UI状态
     * @param {boolean} isLoggedIn - 是否已登录
     */
    updateLoginUI(isLoggedIn) {
        if (isLoggedIn) {
            this.elements.loginPanel.style.display = 'none';
            this.elements.workspace.style.display = 'block';
            this.elements.userInfo.style.display = 'flex';
            this.elements.persistentEmailContainer.style.display = 'flex';

            const userEmail = getAppState().get('auth.user.email');
            this.elements.currentUser.textContent = userEmail || '已登录';

            // 加载持久化邮箱
            this.loadPersistentEmail();

        } else {
            this.elements.loginPanel.style.display = 'flex';
            this.elements.workspace.style.display = 'none';
            this.elements.userInfo.style.display = 'none';
            this.elements.persistentEmailContainer.style.display = 'none';
        }
    }
    
    /**
     * 填充表单选项（优化版本，限制子分类范围）
     */
    populateFormOptions() {
        // 获取系统数据，如果AppState中没有，则使用ApiService的静态数据作为降级
        let systemData = getAppState().get('systemData') || {};

        // 检查数据完整性，如果不完整则使用ApiService的静态数据
        const isDataIncomplete = !systemData.backendUsers || systemData.backendUsers.length === 0 ||
                                !systemData.carTypes || systemData.carTypes.length === 0;

        if (isDataIncomplete) {
            getLogger().log('检测到DOM未被填充，需要从AppState重新填充选项', 'warning', {
                backendUsersCount: (systemData.backendUsers || []).length,
                carTypesCount: (systemData.carTypes || []).length,
                subCategoriesCount: (systemData.subCategories || []).length
            });

            // 使用ApiService的静态数据作为降级方案
            const apiService = getApiService();
            if (apiService && apiService.staticData) {
                systemData = apiService.staticData;
                getAppState().setSystemData(systemData);
                getLogger().log('已从ApiService静态数据重新填充', 'info', {
                    backendUsersCount: systemData.backendUsers.length,
                    carTypesCount: systemData.carTypes.length,
                    subCategoriesCount: systemData.subCategories.length
                });
            }
        }

        // 填充后台用户
        const backendUsers = systemData.backendUsers || [];
        this.populateSelect(this.elements.inchargeByBackendUserId, backendUsers, 'id', 'name');

        // 设置默认后台用户
        const defaultUserId = getAppState().getDefaultBackendUser();
        if (defaultUserId) {
            this.elements.inchargeByBackendUserId.value = defaultUserId;
        }

        // 填充子分类（仅限制的三种服务类型）
        const allowedSubCategories = getApiService().getAllowedSubCategories();
        this.populateSelect(this.elements.subCategoryId, allowedSubCategories, 'id', 'name');

        // 添加子分类描述提示
        this.addSubCategoryTooltips();

        // 填充车型（使用统一的systemData）
        const carTypes = systemData.carTypes || [];
        this.populateSelect(this.elements.carTypeId, carTypes, 'id', 'name');

        // 填充行驶区域（使用统一的systemData）
        const drivingRegions = systemData.drivingRegions || [];
        this.populateSelect(this.elements.drivingRegionId, drivingRegions, 'id', 'name');

        // 填充语言（多选，使用统一的systemData）
        const languages = systemData.languages || [];
        this.populateSelect(this.elements.languagesIdArray, languages, 'id', 'name');

        // 设置默认日期
        this.setDefaultDate();

        // 记录填充结果
        getLogger().log('表单选项填充完成', 'info', {
            backendUsers: backendUsers.length,
            subCategories: allowedSubCategories.length,
            carTypes: carTypes.length,
            drivingRegions: drivingRegions.length,
            languages: languages.length
        });

        // 新增：OTA渠道下拉选项填充
        this.populateOtaChannelOptions();
    }

    /**
     * 添加子分类选择提示
     */
    addSubCategoryTooltips() {
        if (this.elements.subCategoryId) {
            const label = this.elements.subCategoryId.parentElement.querySelector('label');
            if (label) {
                label.title = '仅支持：接机(Pickup)、送机(Dropoff)、包车(Charter)三种服务类型';
                label.style.cursor = 'help';
            }
        }
    }
    
    /**
     * 填充OTA渠道选项和默认值
     * 根据当前登录用户自动设置OTA渠道
     */
    populateOtaChannelOptions() {
        const appState = getAppState();
        const user = appState.get('auth.user');
        let otaConfig = null;
        let matchedUserId = null;
        
        // 调试日志：输出当前用户信息
        getLogger().log('开始填充OTA渠道选项', 'info', {
            user: user,
            hasOtaMapping: !!(window.OTA && window.OTA.otaChannelMapping)
        });
        
        if (user) {
            // 优先使用用户ID
            if (user.id && window.OTA && window.OTA.otaChannelMapping) {
                otaConfig = window.OTA.otaChannelMapping.getConfig(user.id);
                matchedUserId = user.id;
                getLogger().log('尝试通过用户ID获取OTA配置', 'info', {
                    userId: user.id,
                    otaConfig: otaConfig
                });
            }
            
            // 如果没有用户ID或配置，通过邮箱查找对应的后台用户ID
            if (!otaConfig && user.email) {
                const systemData = appState.get('systemData') || {};
                const backendUsers = systemData.backendUsers || [];
                const matchedUser = backendUsers.find(u => u.email === user.email);
                
                getLogger().log('尝试通过邮箱匹配后台用户', 'info', {
                    email: user.email,
                    backendUsersCount: backendUsers.length,
                    matchedUser: matchedUser
                });
                
                if (matchedUser && window.OTA && window.OTA.otaChannelMapping) {
                    otaConfig = window.OTA.otaChannelMapping.getConfig(matchedUser.id);
                    matchedUserId = matchedUser.id;
                    getLogger().log('通过邮箱匹配到后台用户ID', 'info', {
                        email: user.email,
                        backendUserId: matchedUser.id,
                        otaConfig: otaConfig
                    });
                }
            }
        }
        
        // 调试日志：显示最终匹配结果
        getLogger().log('OTA配置匹配结果', 'info', {
            matchedUserId: matchedUserId,
            hasOtaConfig: !!otaConfig,
            otaConfig: otaConfig
        });
        
        // 如果没有找到配置，使用默认配置
        
        // 填充OTA渠道下拉选项
        if (this.elements.otaChannel) {
            // 清空下拉选项（不添加默认的"请选择"选项）
            this.elements.otaChannel.innerHTML = '';
            
            if (otaConfig && Array.isArray(otaConfig.options)) {
                // 使用用户专属的OTA渠道选项
                otaConfig.options.forEach(opt => {
                    const option = document.createElement('option');
                    option.value = opt;
                    option.textContent = opt;
                    this.elements.otaChannel.appendChild(option);
                });
                
                // 设置默认值
                if (otaConfig.default) {
                    this.elements.otaChannel.value = otaConfig.default;
                    getLogger().log('OTA渠道默认值已设置', 'success', {
                        userId: matchedUserId,
                        email: user?.email,
                        defaultOta: otaConfig.default,
                        availableOptions: otaConfig.options.length
                    });
                }
            } else {
                // 使用通用OTA渠道选项，为这些账号添加默认选项
                this.elements.otaChannel.innerHTML = '<option value="">请选择OTA渠道</option>';
                
                if (window.OTA && window.OTA.otaChannelMapping && window.OTA.otaChannelMapping.commonChannels) {
                    window.OTA.otaChannelMapping.commonChannels.forEach(opt => {
                        const option = document.createElement('option');
                        option.value = opt;
                        option.textContent = opt;
                        this.elements.otaChannel.appendChild(option);
                    });
                }
                
                getLogger().log('未找到用户的OTA配置，使用通用选项', 'info', {
                    userId: matchedUserId,
                    email: user?.email
                });
            }
        }
        
        // 清空自定义输入框
        if (this.elements.otaChannelCustom) {
            this.elements.otaChannelCustom.value = '';
        }
    }
    
    /**
     * 填充下拉选择框
     * @param {HTMLSelectElement} selectElement - 选择框元素
     * @param {Array} options - 选项数组
     * @param {string} valueField - 值字段名
     * @param {string} textField - 显示文本字段名
     */
    populateSelect(selectElement, options, valueField, textField) {
        if (!selectElement || !Array.isArray(options)) return;
        
        // 保持默认选项
        const defaultOptions = Array.from(selectElement.children).filter(option => option.value === '');
        
        // 清空现有选项（除默认选项外）
        selectElement.innerHTML = '';
        defaultOptions.forEach(option => selectElement.appendChild(option));
        
        // 添加新选项
        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option[valueField];
            optionElement.textContent = option[textField];
            selectElement.appendChild(optionElement);
        });
    }
    
    /**
     * 从解析数据填充表单
     * @param {object} data - 解析后的数据
     */
    fillFormFromData(data) {
        // 字段映射表：AI返回字段名 → HTML元素ID
        const fieldMapping = {
            // 基本映射（snake_case → camelCase）
            'sub_category_id': 'subCategoryId',
            'ota_reference_number': 'otaReferenceNumber',
            'ota': 'otaChannel', // 修正：OTA渠道字段映射，使用正确的API字段名
            'car_type_id': 'carTypeId',
            'incharge_by_backend_user_id': 'inchargeByBackendUserId',
            'customer_name': 'customerName',
            'customer_contact': 'customerContact',
            'customer_email': 'customerEmail',
            'flight_info': 'flightInfo',
            'pickup': 'pickup',
            'passenger_number': 'passengerNumber',
            'passenger_count': 'passengerNumber', // 别名映射
            'luggage_number': 'luggageNumber',
            'luggage_count': 'luggageNumber', // 别名映射
            'driving_region_id': 'drivingRegionId',
            'languages_id_array': 'languagesIdArray',
            'ota_price': 'otaPrice',
            'driver_fee': 'driverFee',
            'driver_collect': 'driverCollect',
            'extra_requirement': 'extraRequirement',
            'remark': 'extraRequirement', // 别名映射
            'tour_guide': 'tourGuide',
            'baby_chair': 'babyChair',
            'meet_and_greet': 'meetAndGreet',

            // 特殊映射（解决字段名不匹配问题）
            'dropoff': 'destination',        // dropoff → destination
            'pickup_date': 'date',          // pickup_date → date
            'pickup_time': 'time',          // pickup_time → time
            'departure_time': 'time',       // 新增：起飞时间映射（双时间情况下优先级处理）
            'arrival_time': 'time',         // 新增：降落时间映射（双时间情况下优先级处理）
            'flight_time': 'time'           // 航班时间映射
        };

        // 动态映射所有字段
        for (const snakeCaseKey in data) {
            if (Object.prototype.hasOwnProperty.call(data, snakeCaseKey)) {
                // 优先使用映射表，如果没有则使用自动转换
                const elementKey = fieldMapping[snakeCaseKey] || this.snakeToCamel(snakeCaseKey);
                const element = this.elements[elementKey];

                // 记录字段映射过程（调试用）
                if (fieldMapping[snakeCaseKey]) {
                    getLogger().log(`字段映射: ${snakeCaseKey} → ${elementKey}`, 'info', {
                        value: data[snakeCaseKey],
                        elementFound: !!element
                    });
                }

                if (element) {
                    // 处理复选框
                    if (element.type === 'checkbox') {
                        element.checked = Boolean(data[snakeCaseKey]);
                    } else if (element.tagName === 'SELECT') {
                        // 特殊处理：语言多选字段
                        if (elementKey === 'languagesIdArray' && element.multiple) {
                            this.fillLanguageMultiSelect(element, data[snakeCaseKey], snakeCaseKey, data);
                        } else {
                            // 普通单选下拉框处理
                            if (data[snakeCaseKey] !== undefined) {
                                // 检查返回值是否在选项中
                                const optionExists = Array.from(element.options).some(opt => opt.value == data[snakeCaseKey]);
                                if(optionExists) {
                                    element.value = data[snakeCaseKey];
                                } else {
                                    getLogger().log(`下拉框值匹配警告: ${elementKey}字段值"${data[snakeCaseKey]}"在下拉框中未找到`, 'warning', {
                                        field: elementKey,
                                        originalField: snakeCaseKey,
                                        value: data[snakeCaseKey],
                                        availableOptions: Array.from(element.options).map(opt => ({ value: opt.value, text: opt.textContent }))
                                    });

                                    // 尝试应用默认值作为降级方案
                                    const defaultValue = this.getDefaultValueForField(elementKey, data);
                                    if (defaultValue && Array.from(element.options).some(opt => opt.value == defaultValue)) {
                                        element.value = defaultValue;
                                        getLogger().log(`已应用默认值: ${elementKey} = ${defaultValue}`, 'info');
                                    }
                                }
                            }
                        }
                    } else {
                        // 普通输入框/文本域
                        if (data[snakeCaseKey] !== undefined) {
                            // 特殊处理日期时间格式
                            if (elementKey === 'date' && data[snakeCaseKey]) {
                                // 确保日期格式为YYYY-MM-DD
                                const dateValue = this.formatDateForInput(data[snakeCaseKey]);
                                element.value = dateValue;
                                getLogger().log(`日期字段填充: ${snakeCaseKey} → ${elementKey}`, 'info', {
                                    original: data[snakeCaseKey],
                                    formatted: dateValue
                                });
                            } else if (elementKey === 'time' && data[snakeCaseKey]) {
                                // 时间字段特殊处理：双时间情况下优先级处理
                                const timeValue = this.formatTimeForInput(data[snakeCaseKey]);
                                
                                // 双时间优先级：pickup_time > arrival_time/departure_time > flight_time
                                if (snakeCaseKey === 'pickup_time') {
                                    // 最高优先级：接送时间
                                    element.value = timeValue;
                                    getLogger().log(`时间字段填充(最高优先级): ${snakeCaseKey} → ${elementKey}`, 'info', {
                                        original: data[snakeCaseKey],
                                        formatted: timeValue
                                    });
                                } else if (['arrival_time', 'departure_time'].includes(snakeCaseKey)) {
                                    // 中等优先级：双时间字段，但需要检查是否已有pickup_time
                                    if (!data.pickup_time && !element.value) {
                                        element.value = timeValue;
                                        getLogger().log(`时间字段填充(中等优先级): ${snakeCaseKey} → ${elementKey}`, 'info', {
                                            original: data[snakeCaseKey],
                                            formatted: timeValue,
                                            note: '双时间字段，将由航班逻辑进一步处理'
                                        });
                                    }
                                } else if (snakeCaseKey === 'flight_time') {
                                    // 最低优先级：仅在没有其他时间字段时使用
                                    if (!data.pickup_time && !data.arrival_time && !data.departure_time && !element.value) {
                                        element.value = timeValue;
                                        getLogger().log(`时间字段填充(最低优先级): ${snakeCaseKey} → ${elementKey}`, 'info', {
                                            original: data[snakeCaseKey],
                                            formatted: timeValue
                                        });
                                    }
                                } else {
                                    // 其他时间相关字段
                                element.value = timeValue;
                                getLogger().log(`时间字段填充: ${snakeCaseKey} → ${elementKey}`, 'info', {
                                    original: data[snakeCaseKey],
                                    formatted: timeValue
                                });
                                }
                            } else {
                                element.value = data[snakeCaseKey];
                            }
                        }
                    }
                } else {
                    // 特殊处理：OTA渠道字段
                    if (snakeCaseKey === 'ota' && data[snakeCaseKey]) {
                        getLogger().log(`特殊处理OTA渠道字段: ${snakeCaseKey}`, 'info', { value: data[snakeCaseKey] });
                        this.fillOtaChannelField(data[snakeCaseKey]);
                    } else {
                        // 记录未找到的元素（调试用）
                        getLogger().log(`未找到对应元素: ${snakeCaseKey} → ${elementKey}`, 'warning', {
                            originalField: snakeCaseKey,
                            mappedField: elementKey,
                            value: data[snakeCaseKey]
                        });
                    }
                }
            }
        }

        // --- 特殊处理：仅在AI/后端未返回时才用本地推荐/默认 ---
        // 子分类ID校验
        if (!data.sub_category_id && this.elements.subCategoryId) {
            // 本地默认：接机服务
            this.elements.subCategoryId.value = 2;
            getLogger().log('未返回子分类ID，使用本地默认', 'info');
        }
        // 车型推荐
        if (!data.car_type_id && this.elements.carTypeId) {
            const recommendedCarType = getApiService().recommendCarType(data.passenger_number);
            this.elements.carTypeId.value = recommendedCarType;
            getLogger().log('未返回车型ID，使用本地推荐', 'info', { recommendedCarType });
        }
        // 负责人默认
        if (!data.incharge_by_backend_user_id && this.elements.inchargeByBackendUserId) {
            const defaultBackendUserId = getApiService().getDefaultBackendUserId();
            this.elements.inchargeByBackendUserId.value = defaultBackendUserId;
            getLogger().log('未返回负责人ID，使用本地默认', 'info', { defaultBackendUserId });
        }
        // 语言多选处理已移至主要字段映射循环中，避免重复处理
        // OTA参考号生成逻辑（仅在AI未提供时）
        if (!data.ota_reference_number && this.elements.otaReferenceNumber) {
            const timestamp = Date.now().toString().slice(-6);
            this.elements.otaReferenceNumber.value = `GMH-${timestamp}`;
        }
        
        // 移除客户端航班时间逻辑处理，因为已在Prompt中完成
        // this.handleFlightTimeLogic(data);
    }
    
    /**
     * 收集表单数据
     * @returns {object} 表单数据
     */
    collectFormData() {
        const data = {};
        
        // 基本字段
        const fields = [
            'subCategoryId', 'otaReferenceNumber', 'carTypeId', 'inchargeByBackendUserId',
            'customerName', 'customerContact', 'customerEmail', 'flightInfo',
            'pickup', 'destination', 'date', 'time',
            'passengerNumber', 'luggageNumber', 'drivingRegionId',
            'otaPrice', 'driverFee', 'driverCollect', 'extraRequirement'
        ];
        
        fields.forEach(field => {
            const element = this.elements[field];
            if (element && element.value !== null && element.value.trim() !== '') {
                const apiField = this.camelToSnake(field);
                data[apiField] = element.value.trim();
            }
        });
        
        // 复选框字段
        data.tour_guide = this.elements.tourGuide.checked;
        data.baby_chair = this.elements.babyChair.checked;
        data.meet_and_greet = this.elements.meetAndGreet.checked;
        
        // 语言数组（转换为对象格式以确保GoMyHire API兼容性）
        const selectedLanguages = Array.from(this.elements.languagesIdArray.selectedOptions)
            .map(option => parseInt(option.value))
            .filter(value => !isNaN(value));

        if (selectedLanguages.length > 0) {
            // 转换为对象格式：{"0":"2","1":"4"}
            const languagesObject = {};
            selectedLanguages.forEach((id, index) => {
                languagesObject[index.toString()] = id.toString();
            });
            data.languages_id_array = languagesObject;

            getLogger().log('语言数组已转换为对象格式', 'info', {
                selectedLanguages,
                languagesObject
            });
        }
        
        // 新增：OTA渠道字段收集，优先自定义输入
        let otaChannelValue = '';
        if (this.elements.otaChannelCustom && this.elements.otaChannelCustom.value.trim() !== '') {
            otaChannelValue = this.elements.otaChannelCustom.value.trim();
        } else if (this.elements.otaChannel && this.elements.otaChannel.value.trim() !== '') {
            otaChannelValue = this.elements.otaChannel.value.trim();
        }
        if (otaChannelValue) {
            data.ota = otaChannelValue; // 修正：使用正确的API字段名 ota
        }
        
        // 自动填充负责人ID
        if (!data.incharge_by_backend_user_id && this.elements.inchargeByBackendUserId) {
            const defaultBackendUserId = getApiService().getDefaultBackendUserId();
            this.elements.inchargeByBackendUserId.value = defaultBackendUserId;
            getLogger().log('未返回负责人ID，使用本地默认', 'info', { defaultBackendUserId });
        }
        
        // OTA参考号生成逻辑（仅在AI未提供时）
        if (!data.ota_reference_number && this.elements.otaReferenceNumber) {
            const timestamp = Date.now().toString().slice(-6);
            this.elements.otaReferenceNumber.value = `GMH-${timestamp}`;
        }
        
        return data;
    }
    
    /**
     * 将驼峰命名转换为蛇形命名
     * @param {string} str - 驼峰命名字符串
     * @returns {string} 蛇形命名字符串
     */
    camelToSnake(str) {
        return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
    }

    /**
     * 将蛇形命名转换为驼峰命名
     * @param {string} str - 蛇形命名字符串
     * @returns {string} 驼峰命名字符串
     */
    snakeToCamel(str) {
        return str.replace(/([-_][a-z])/g, (group) => group.toUpperCase().replace('-', '').replace('_', ''));
    }
    
    /**
     * 显示验证错误
     * @param {object} errors - 错误对象
     */
    showValidationErrors(errors) {
        let errorMessages = [];
        Object.entries(errors).forEach(([field, messages]) => {
            errorMessages.push(`${field}: ${messages.join(', ')}`);
            this.showFieldError(field, messages.join(', '));
        });
        
        this.showAlert(`验证失败:\n${errorMessages.join('\n')}`, 'error');
    }
    
    /**
     * 显示字段错误
     * @param {string} fieldName - 字段名
     * @param {string} message - 错误消息
     */
    showFieldError(fieldName, message) {
        const element = this.elements[fieldName];
        if (!element) return;
        
        element.classList.add('error');
        
        // 移除现有错误消息
        const existingError = element.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
        
        // 添加新错误消息
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.textContent = message;
        element.parentNode.appendChild(errorDiv);
    }
    
    /**
     * 清除字段错误
     * @param {string} fieldName - 字段名
     */
    clearFieldError(fieldName) {
        const element = this.elements[fieldName];
        if (!element) return;
        
        element.classList.remove('error');
        
        const errorDiv = element.parentNode.querySelector('.field-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }
    
    /**
     * 设置按钮加载状态
     * @param {HTMLButtonElement} button - 按钮元素
     * @param {boolean} loading - 是否加载中
     */
    setButtonLoading(button, loading) {
        if (!button) return;
        
        const textSpan = button.querySelector('.btn-text');
        const spinner = button.querySelector('.loading-spinner');
        
        if (loading) {
            button.disabled = true;
            if (textSpan) textSpan.style.display = 'none';
            if (spinner) spinner.style.display = 'inline';
        } else {
            button.disabled = false;
            if (textSpan) textSpan.style.display = 'inline';
            if (spinner) spinner.style.display = 'none';
        }
    }
    
    /**
     * 更新Gemini状态显示
     * @param {string} status - 状态文本
     */
    updateGeminiStatus(status = null) {
        if (!this.elements.geminiStatus) return;
        
        if (status) {
            this.elements.geminiStatus.textContent = status;
        } else {
            const available = getGeminiService().isAvailable();
            const realtimeEnabled = getGeminiService().realtimeConfig?.enabled;
            
            if (available && realtimeEnabled) {
                this.elements.geminiStatus.textContent = '🤖 AI实时分析已启用';
            } else if (available) {
                this.elements.geminiStatus.textContent = '🤖 AI助手已就绪';
            } else {
                this.elements.geminiStatus.textContent = '⚠️ 请设置Gemini API密钥';
            }
        }
    }
    
    /**
     * 更新数据状态
     */
    updateDataStatus() {
        const hasData = getAppState().get('systemData.lastUpdated') !== null;
        const status = hasData ? '📊 数据已就绪' : '📊 等待数据';
        this.elements.dataStatus.textContent = status;
    }
    
    /**
     * 更新连接状态
     * @param {boolean} connected - 是否已连接
     */
    updateConnectionStatus(connected) {
        const status = connected ? '🔌 已连接' : '🔌 未连接';
        this.elements.connectionStatus.textContent = status;
    }
    
    /**
     * 更新最后更新时间
     */
    updateLastUpdateTime() {
        const lastUpdate = getAppState().get('systemData.lastUpdated');
        if (lastUpdate) {
            const time = getUtils().formatTime(new Date(lastUpdate));
            this.elements.lastUpdate.textContent = `⏰ ${time}`;
        } else {
            this.elements.lastUpdate.textContent = '⏰ --:--';
        }
    }
    
    /**
     * @function updateOrderForm
     * @description 从应用状态中获取当前订单数据并更新表单UI。
     *              这是对 fillFormFromData 的一个封装，用于响应状态变化。
     * @private
     */
    async updateOrderForm() {
        try {
            const currentOrder = getAppState().get('currentOrder');
            if (!currentOrder || Object.keys(currentOrder).length === 0) {
                getLogger().log('当前无订单数据，跳过表单更新');
                return;
            }

            // 检查系统数据是否已加载到 AppState
            const hasSystemData = getAppState().get('systemData.lastUpdated');

            // 如果 AppState 中没有数据，则先加载
            if (!hasSystemData) {
                getLogger().log('AppState中无系统数据，开始加载', 'info');
                try {
                    await getApiService().getAllSystemData();
                    this.populateFormOptions(); // 第一次填充下拉列表
                    getLogger().log('系统数据和表单选项加载完成', 'success');
                } catch (error) {
                    getLogger().logError('加载系统数据失败，无法填充表单', error);
                    this.showAlert('无法加载基础数据，请稍后重试', 'error');
                    return; // 加载失败，直接返回，不继续填充
                }
            } else {
                // 如果 AppState 中有数据，但 DOM 可能未更新（例如，在页面切换后），则确保 DOM 被填充
                const carTypeOptions = this.elements.carTypeId?.options?.length || 0;
                if (carTypeOptions <= 1) { // 用一个下拉框作为代表
                    getLogger().log('检测到DOM未被填充，从AppState重新填充选项', 'info');
                    this.populateFormOptions();
                }
            }
            
            // 此时，可以安全地认为下拉列表已填充
            const formData = currentOrder.formData || currentOrder.parsedData;
            if (formData) {
                getLogger().log('开始从当前订单状态更新表单');
                this.fillFormFromData(formData);
                getLogger().log('表单更新完成');
            }
        } catch (error) {
            getLogger().logError(error, '更新订单表单时出错');
        }
    }

    /**
     * 更新主题图标
     * @param {string} theme - 主题名称
     */
    updateThemeIcon(theme) {
        if (this.elements.themeToggle) {
            this.elements.themeToggle.textContent = theme === 'light' ? '🌙' : '☀️';
            this.elements.themeToggle.title = theme === 'light' ? '切换到暗色主题' : '切换到亮色主题';
        }
    }
    
    /**
     * 显示模态框
     * @param {string} title - 标题
     * @param {string} content - 内容
     * @param {object} options - 选项
     */
    showModal(title, content, options = {}) {
        this.elements.modalTitle.textContent = title;
        this.elements.modalBody.innerHTML = content;
        
        // 配置按钮
        if (options.showCancel !== false) {
            this.elements.modalCancel.style.display = 'inline-flex';
        } else {
            this.elements.modalCancel.style.display = 'none';
        }
        
        if (options.confirmText) {
            this.elements.modalConfirm.textContent = options.confirmText;
        } else {
            this.elements.modalConfirm.textContent = '确认';
        }
        
        if (options.onConfirm) {
            this.elements.modalConfirm.onclick = () => {
                options.onConfirm();
                this.hideModal();
            };
        }
        
        // 移除hidden类并设置显示
        this.elements.modal.classList.remove('hidden');
        this.elements.modal.style.display = 'flex';
        this.currentModal = this.elements.modal;

        console.log('模态框显示后的状态:', {
            classList: this.elements.modal.className,
            display: this.elements.modal.style.display
        });
        
        // 自动关闭
        if (options.autoClose) {
            setTimeout(() => this.hideModal(), options.autoClose);
        }
    }
    
    /**
     * 隐藏模态框
     */
    hideModal() {
        if (this.elements.modal) {
            // 添加hidden类并设置隐藏
            this.elements.modal.classList.add('hidden');
            this.elements.modal.style.display = 'none';
            this.currentModal = null;

            console.log('模态框隐藏后的状态:', {
                classList: this.elements.modal.className,
                display: this.elements.modal.style.display
            });
        }
    }
    
    /**
     * 显示确认对话框
     * @param {string} title - 标题
     * @param {string} message - 消息
     * @param {function} onConfirm - 确认回调
     * @param {function} onCancel - 取消回调
     */
    showConfirm(title, message, onConfirm, onCancel = null) {
        console.log('showConfirm 被调用:', { title, message });
        console.log('modal元素:', this.elements.modal);
        console.log('modal初始状态:', {
            classList: this.elements.modal?.className,
            display: this.elements.modal?.style.display,
            zIndex: window.getComputedStyle(this.elements.modal).zIndex
        });
        console.log('modalTitle元素:', this.elements.modalTitle);
        console.log('modalBody元素:', this.elements.modalBody);
        console.log('modalConfirm元素:', this.elements.modalConfirm);

        try {
            this.showModal(title, `<p>${message}</p>`, {
                onConfirm: () => {
                    console.log('确认按钮被点击');
                    if (onConfirm) {
                        onConfirm();
                    }
                },
                confirmText: '确认'
            });

            if (onCancel) {
                this.elements.modalCancel.onclick = () => {
                    console.log('取消按钮被点击');
                    onCancel();
                    this.hideModal();
                };
            }

            console.log('确认对话框显示成功');
        } catch (error) {
            console.error('显示确认对话框时发生错误:', error);
        }
    }
    
    /**
     * 显示提示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, error, warning, info)
     * @param {number} duration - 显示时长（毫秒）
     */
    showAlert(message, type = 'info', duration =  5000) {
        // 创建提示元素
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.innerHTML = `
            <span class="alert-icon">${this.getAlertIcon(type)}</span>
            <span class="alert-message">${message}</span>
            <button class="alert-close">✕</button>
        `;
        
        // 添加样式
        alert.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: var(--spacing-3);
            box-shadow: var(--shadow-lg);
            z-index: 10000;
            max-width: 400px;
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
            transform: translateX(100%);
            transition: transform var(--transition-normal);
        `;
        
        // 根据类型设置颜色
        switch (type) {
            case 'success':
                alert.style.borderLeftColor = 'var(--color-success)';
                break;
            case 'error':
                alert.style.borderLeftColor = 'var(--color-error)';
                break;
            case 'warning':
                alert.style.borderLeftColor = 'var(--color-warning)';
                break;
            default:
                alert.style.borderLeftColor = 'var(--color-info)';
        }
        
        // 添加到页面
        document.body.appendChild(alert);
        
        // 动画显示
        setTimeout(() => {
            alert.style.transform = 'translateX(0)';
        }, 10);
        
        // 关闭按钮事件
        const closeBtn = alert.querySelector('.alert-close');
        const closeAlert = () => {
            alert.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 300);
        };
        
        closeBtn.addEventListener('click', closeAlert);
        
        // 自动关闭
        if (duration > 0) {
            setTimeout(closeAlert, duration);
        }
        
        // 记录日志
        getLogger().log(message, type);
    }
    
    /**
     * 获取提示图标
     * @param {string} type - 提示类型
     * @returns {string} 图标
     */
    getAlertIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        return icons[type] || icons.info;
    }
    
    /**
     * 显示API密钥设置提示
     */
    showApiKeyPrompt() {
        const content = `
            <div class="api-key-prompt">
                <p>要使用AI解析功能，请输入Gemini API密钥：</p>
                <div class="form-group">
                    <label for="geminiApiKey">Gemini API Key:</label>
                    <input type="password" id="geminiApiKey" placeholder="输入您的Gemini API密钥">
                    <small>
                        获取API密钥: <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a>
                    </small>
                </div>
            </div>
        `;
        
        this.showModal('设置Gemini API密钥', content, {
            confirmText: '保存',
            onConfirm: () => {
                const apiKey = document.getElementById('geminiApiKey').value.trim();
                if (apiKey) {
                    getGeminiService().setApiKey(apiKey);
                    this.updateGeminiStatus();
                    this.showAlert('API密钥已保存，实时分析已启用', 'success');
                } else {
                    this.showAlert('请输入有效的API密钥', 'warning');
                }
            }
        });
    }
    
    /**
     * 获取名称映射方法
     */
    getSubCategoryName(id) {
        const subCategories = getAppState().get('systemData.subCategories') || [];
        const category = subCategories.find(cat => cat.id == id);
        return category ? category.name : `子分类 ${id}`;
    }
    
    getCarTypeName(id) {
        const carTypes = getAppState().get('systemData.carTypes') || [];
        const carType = carTypes.find(car => car.id == id);
        return carType ? carType.name : `车型 ${id}`;
    }
    
    getBackendUserName(id) {
        const users = getAppState().get('systemData.backendUsers') || [];
        const user = users.find(u => u.id == id);
        return user ? user.name : `用户 ${id}`;
    }

    getDrivingRegionName(id) {
        const regions = getAppState().get('systemData.drivingRegions') || [];
        const region = regions.find(r => r.id == id);
        return region ? region.name : `区域 ${id}`;
    }

    getLanguageNames(languagesIdArray) {
        if (!languagesIdArray) return 'N/A';

        const languages = getAppState().get('systemData.languages') || [];
        let languageIds = [];

        // 处理不同格式的语言数组
        if (Array.isArray(languagesIdArray)) {
            languageIds = languagesIdArray;
        } else if (typeof languagesIdArray === 'object') {
            // 对象格式：{"0":"2","1":"4"}
            languageIds = Object.values(languagesIdArray).map(id => parseInt(id));
        }

        const languageNames = languageIds.map(id => {
            const language = languages.find(l => l.id == id);
            return language ? language.name : `语言${id}`;
        });

        return languageNames.length > 0 ? languageNames.join(', ') : 'N/A';
    }

    /**
     * 填充语言多选字段
     * @param {HTMLSelectElement} element - 多选下拉框元素
     * @param {any} languageData - 语言数据（可能是数组、对象或空值）
     * @param {string} originalField - 原始字段名
     * @param {object} allData - 完整数据对象
     */
    fillLanguageMultiSelect(element, languageData, originalField, allData) {
        // 清除现有选择
        Array.from(element.options).forEach(option => option.selected = false);

        let languageIds = [];

        // 解析不同格式的语言数据
        if (Array.isArray(languageData)) {
            languageIds = languageData.map(id => parseInt(id)).filter(id => !isNaN(id));
        } else if (typeof languageData === 'object' && languageData !== null) {
            // 对象格式：{"0":"2","1":"4"}
            languageIds = Object.values(languageData).map(id => parseInt(id)).filter(id => !isNaN(id));
        } else if (typeof languageData === 'string' || typeof languageData === 'number') {
            // 单个ID
            const id = parseInt(languageData);
            if (!isNaN(id)) languageIds = [id];
        }

        // 如果没有有效的语言ID，使用智能检测
        if (languageIds.length === 0) {
            getLogger().log('语言数据为空，启用智能检测', 'info', {
                originalField,
                languageData,
                customerName: allData.customer_name
            });

            // 使用Gemini服务的智能检测
            if (window.OTA && window.OTA.geminiService) {
                const smartLanguages = window.OTA.geminiService.getLanguagesIdArray(
                    allData.remark || allData.extra_requirement,
                    allData.customer_name
                );
                languageIds = Object.values(smartLanguages).map(id => parseInt(id)).filter(id => !isNaN(id));

                getLogger().log('智能语言检测完成', 'success', {
                    customerName: allData.customer_name,
                    detectedLanguages: smartLanguages,
                    languageIds
                });
            }
        }

        // 应用语言选择
        let appliedCount = 0;
        languageIds.forEach(langId => {
            const option = element.querySelector(`option[value="${langId}"]`);
            if (option) {
                option.selected = true;
                appliedCount++;
            } else {
                getLogger().log(`语言选项未找到: ID ${langId}`, 'warning', {
                    languageId: langId,
                    availableOptions: Array.from(element.options).map(opt => ({
                        value: opt.value,
                        text: opt.textContent
                    }))
                });
            }
        });

        getLogger().log('语言多选字段填充完成', 'info', {
            originalField,
            inputData: languageData,
            parsedIds: languageIds,
            appliedCount,
            totalIds: languageIds.length
        });
    }

    /**
     * 格式化日期为HTML input[type="date"]所需的格式 (YYYY-MM-DD)
     * @param {string} dateString - 原始日期字符串
     * @returns {string} 格式化后的日期字符串
     */
    formatDateForInput(dateString) {
        try {
            // 尝试解析各种日期格式
            const date = new Date(dateString);
            if (isNaN(date.getTime())) {
                getLogger().log('日期格式无效，使用今天日期', 'warning', { input: dateString });
                return new Date().toISOString().split('T')[0];
            }
            return date.toISOString().split('T')[0]; // YYYY-MM-DD
        } catch (error) {
            getLogger().log('日期解析失败，使用今天日期', 'error', { input: dateString, error: error.message });
            return new Date().toISOString().split('T')[0];
        }
    }
    
    /**
     * 设置默认日期为当前年份（如果没有日期值）
     */
    setDefaultDate() {
        if (this.elements.date && !this.elements.date.value) {
            const today = new Date();
            this.elements.date.value = today.toISOString().split('T')[0];
            getLogger().log('已设置默认日期为今天', 'info', { date: this.elements.date.value });
        }
    }
    
    /**
     * 获取服务类型
     * @param {string|number} subCategoryId - 子分类ID
     * @returns {string|null} 服务类型：'pickup', 'dropoff', 'charter' 或 null
     */
    getServiceType(subCategoryId) {
        const id = parseInt(subCategoryId);
        switch (id) {
            case 2: return 'pickup';   // 接机
            case 3: return 'dropoff';  // 送机
            case 4: return 'charter';  // 包车
            default: return null;
        }
    }

    /**
     * 格式化时间为HTML input[type="time"]所需的格式 (HH:MM)
     * @param {string} timeString - 原始时间字符串
     * @returns {string} 格式化后的时间字符串
     */
    formatTimeForInput(timeString) {
        try {
            // 处理各种时间格式
            if (timeString.includes(':')) {
                // 已经是HH:MM或HH:MM:SS格式
                const timeParts = timeString.split(':');
                const hours = timeParts[0].padStart(2, '0');
                const minutes = timeParts[1].padStart(2, '0');
                return `${hours}:${minutes}`;
            } else {
                // 尝试解析其他格式
                const date = new Date(`1970-01-01 ${timeString}`);
                if (!isNaN(date.getTime())) {
                    return date.toTimeString().slice(0, 5); // HH:MM
                }
            }

            getLogger().log('时间格式无效，使用当前时间', 'warning', { input: timeString });
            return new Date().toTimeString().slice(0, 5);
        } catch (error) {
            getLogger().log('时间解析失败，使用当前时间', 'error', { input: timeString, error: error.message });
            return new Date().toTimeString().slice(0, 5);
        }
    }

    /**
     * 获取字段的默认值（用于下拉框值匹配失败时的降级方案）
     * @param {string} fieldName - 字段名（camelCase）
     * @param {object} data - 当前数据对象
     * @returns {string|null} 默认值
     */
    getDefaultValueForField(fieldName, data) {
        switch (fieldName) {
            case 'carTypeId':
                // 基于乘客人数推荐车型，默认5座
                return getApiService().recommendCarType(data.passenger_number || data.passenger_count);

            case 'drivingRegionId':
                // 默认吉隆坡区域
                return '1';

            case 'languagesIdArray':
                // 默认英文
                return '2';

            case 'subCategoryId':
                // 默认接机服务
                return '2';

            case 'inchargeByBackendUserId':
                // 使用当前登录用户对应的后台用户ID
                return getAppState().getDefaultBackendUser() || '37';

            default:
                return null;
        }
    }

    /**
     * 填充OTA渠道字段
     * @param {string} otaChannel - OTA渠道值
     */
    fillOtaChannelField(otaChannel) {
        // 优先尝试在下拉框中查找匹配项
        if (this.elements.otaChannel) {
            const optionExists = Array.from(this.elements.otaChannel.options).some(opt => opt.value === otaChannel);
            if (optionExists) {
                this.elements.otaChannel.value = otaChannel;
                // 清空自定义输入框
                if (this.elements.otaChannelCustom) {
                    this.elements.otaChannelCustom.value = '';
                }
                getLogger().log('OTA渠道已填充到下拉框', 'info', { otaChannel });
            } else {
                // 下拉框中没有匹配项，填充到自定义输入框
                this.elements.otaChannel.value = '';
                if (this.elements.otaChannelCustom) {
                    this.elements.otaChannelCustom.value = otaChannel;
                }
                getLogger().log('OTA渠道已填充到自定义输入框', 'info', { otaChannel });
            }
        } else if (this.elements.otaChannelCustom) {
            // 如果下拉框不存在，直接填充到自定义输入框
            this.elements.otaChannelCustom.value = otaChannel;
            getLogger().log('OTA渠道已填充到自定义输入框（下拉框不存在）', 'info', { otaChannel });
        }
    }

    /**
     * 计算数据置信度（基于非空字段数量）
     * @param {object} orderData - 订单数据
     * @returns {number} 置信度百分比 (0-100)
     */
    calculateDataConfidence(orderData) {
        if (!orderData || typeof orderData !== 'object') {
            return 0;
        }

        // 定义重要字段及其权重
        const importantFields = {
            customer_name: 15,
            customer_phone: 15,
            pickup_date: 15,
            pickup_time: 10,
            pickup_location: 15,
            dropoff_location: 15,
            passenger_count: 5,
            car_type_id: 5,
            sub_category_id: 5
        };

        let totalWeight = 0;
        let filledWeight = 0;

        for (const [field, weight] of Object.entries(importantFields)) {
            totalWeight += weight;
            const value = orderData[field];
            if (value !== null && value !== undefined && value !== '' && value !== 0) {
                filledWeight += weight;
            }
        }

        return Math.round((filledWeight / totalWeight) * 100);
    }

    /**
     * 保存账号信息到本地存储
     * @param {string} email - 邮箱地址
     * @param {boolean} rememberMe - 是否保持登录状态
     */
    saveAccountInfo(email, rememberMe) {
        try {
            if (rememberMe) {
                // 保存邮箱到本地存储
                localStorage.setItem('ota-saved-email', email);
                localStorage.setItem('ota-remember-me', 'true');
                getLogger().log('账号信息已保存', 'info', { email, rememberMe });
            } else {
                // 如果不保持登录状态，清除保存的邮箱
                localStorage.removeItem('ota-saved-email');
                localStorage.removeItem('ota-remember-me');
            }
            
            // 更新清除按钮显示状态
            this.updateClearSavedButton();
        } catch (error) {
            getLogger().logError('保存账号信息失败', error);
        }
    }

    /**
     * 加载保存的账号信息
     */
    loadSavedAccountInfo() {
        try {
            const savedEmail = localStorage.getItem('ota-saved-email');
            const rememberMe = localStorage.getItem('ota-remember-me') === 'true';
            
            if (savedEmail && this.elements.emailInput) {
                this.elements.emailInput.value = savedEmail;
                getLogger().log('已加载保存的账号信息', 'info', { email: savedEmail });
            }
            
            if (this.elements.rememberMe) {
                this.elements.rememberMe.checked = rememberMe;
            }
            
            // 更新清除按钮的显示状态
            this.updateClearSavedButton();
        } catch (error) {
            getLogger().logError('加载账号信息失败', error);
        }
    }

    /**
     * 更新清除保存账号按钮的显示状态
     */
    updateClearSavedButton() {
        if (this.elements.clearSavedBtn) {
            const hasSavedEmail = localStorage.getItem('ota-saved-email');
            this.elements.clearSavedBtn.style.display = hasSavedEmail ? 'inline-block' : 'none';
        }
    }

    /**
     * 清除保存的账号信息
     */
    clearSavedAccountInfo() {
        try {
            localStorage.removeItem('ota-saved-email');
            localStorage.removeItem('ota-remember-me');
            
            if (this.elements.emailInput) {
                this.elements.emailInput.value = '';
            }
            if (this.elements.passwordInput) {
                this.elements.passwordInput.value = '';
            }
            if (this.elements.rememberMe) {
                this.elements.rememberMe.checked = false;
            }
            
            // 更新清除按钮显示状态
            this.updateClearSavedButton();
            
            getLogger().log('已清除保存的账号信息', 'info');
        } catch (error) {
            getLogger().logError('清除账号信息失败', error);
        }
    }

    /**
     * 处理清除保存账号
     */
    handleClearSaved() {
        this.showConfirm(
            '清除保存的账号',
            '确定要清除保存的账号信息吗？',
            () => {
                this.clearSavedAccountInfo();
                this.showAlert('已清除保存的账号信息', 'info');
                this.updateClearSavedButton();
            }
        );
    }

    // 调试方法已移除

    /**
     * 显示历史订单面板
     */
    showHistoryPanel() {
        if (this.elements.historyPanel) {
            this.elements.historyPanel.classList.remove('hidden');
            this.elements.historyPanel.classList.add('show');

            // 防止背景滚动
            document.body.style.overflow = 'hidden';

            // 加载历史订单数据
            this.loadHistoryData();

            getLogger().log('历史订单面板已显示', 'info');
        }
    }

    /**
     * 隐藏历史订单面板
     */
    hideHistoryPanel() {
        if (this.elements.historyPanel) {
            this.elements.historyPanel.classList.add('hidden');
            this.elements.historyPanel.classList.remove('show');

            // 恢复背景滚动
            document.body.style.overflow = '';

            getLogger().log('历史订单面板已隐藏', 'info');
        }
    }

    /**
     * 加载历史订单数据
     */
    loadHistoryData() {
        try {
            const historyManager = getOrderHistoryManager();
            const history = historyManager.getHistory();
            const stats = historyManager.getStatistics();

            // 更新统计信息
            this.updateHistoryStats(stats);

            // 显示订单列表
            this.displayHistoryList(history);

        } catch (error) {
            getLogger().log(`加载历史订单数据失败: ${error.message}`, 'error');
            this.showAlert('加载历史订单数据失败', 'error');
        }
    }

    /**
     * 更新历史订单统计信息
     * @param {Object} stats - 统计数据
     */
    updateHistoryStats(stats) {
        if (this.elements.statTotal) this.elements.statTotal.textContent = stats.total;
        if (this.elements.statToday) this.elements.statToday.textContent = stats.today;
        if (this.elements.statWeek) this.elements.statWeek.textContent = stats.thisWeek;
        if (this.elements.statMonth) this.elements.statMonth.textContent = stats.thisMonth;
    }

    /**
     * 搜索历史订单
     */
    searchHistory() {
        try {
            const criteria = {
                orderId: this.elements.searchOrderId?.value.trim(),
                customerName: this.elements.searchCustomer?.value.trim(),
                dateFrom: this.elements.searchDateFrom?.value,
                dateTo: this.elements.searchDateTo?.value
            };

            const historyManager = getOrderHistoryManager();
            const results = historyManager.searchOrders(criteria);

            this.displayHistoryList(results);

            getLogger().log(`搜索完成，找到 ${results.length} 条记录`, 'info');

        } catch (error) {
            getLogger().log(`搜索历史订单失败: ${error.message}`, 'error');
            this.showAlert('搜索失败', 'error');
        }
    }

    /**
     * 重置搜索条件
     */
    resetHistorySearch() {
        if (this.elements.searchOrderId) this.elements.searchOrderId.value = '';
        if (this.elements.searchCustomer) this.elements.searchCustomer.value = '';
        if (this.elements.searchDateFrom) this.elements.searchDateFrom.value = '';
        if (this.elements.searchDateTo) this.elements.searchDateTo.value = '';

        // 重新加载所有数据
        this.loadHistoryData();
    }

    /**
     * 导出历史订单
     */
    exportHistory() {
        try {
            const historyManager = getOrderHistoryManager();
            const csvData = historyManager.exportOrders(null, 'csv');

            // 创建下载链接
            const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);

            link.setAttribute('href', url);
            link.setAttribute('download', `历史订单_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            this.showAlert('历史订单已导出', 'success');
            getLogger().log('历史订单导出成功', 'success');

        } catch (error) {
            getLogger().log(`导出历史订单失败: ${error.message}`, 'error');
            this.showAlert('导出失败', 'error');
        }
    }

    /**
     * 清空历史订单
     */
    clearHistory() {
        this.showConfirm(
            '清空历史订单',
            '确定要清空所有历史订单吗？此操作不可恢复。',
            () => {
                try {
                    const historyManager = getOrderHistoryManager();
                    historyManager.clearHistory();

                    // 重新加载数据
                    this.loadHistoryData();

                    this.showAlert('历史订单已清空', 'success');
                    getLogger().log('历史订单已清空', 'info');

                } catch (error) {
                    getLogger().log(`清空历史订单失败: ${error.message}`, 'error');
                    this.showAlert('清空失败', 'error');
                }
            }
        );
    }

    /**
     * 验证持久化邮箱格式
     */
    validatePersistentEmail() {
        const email = this.elements.persistentEmail?.value.trim();
        if (!email) {
            this.elements.persistentEmail?.classList.remove('valid', 'invalid');
            return true;
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const isValid = emailRegex.test(email);

        if (isValid) {
            this.elements.persistentEmail?.classList.remove('invalid');
            this.elements.persistentEmail?.classList.add('valid');
        } else {
            this.elements.persistentEmail?.classList.remove('valid');
            this.elements.persistentEmail?.classList.add('invalid');
        }

        return isValid;
    }

    /**
     * 保存持久化邮箱
     */
    savePersistentEmail() {
        const email = this.elements.persistentEmail?.value.trim();

        if (email && !this.validatePersistentEmail()) {
            this.showAlert('邮箱格式不正确', 'warning');
            return;
        }

        try {
            localStorage.setItem('ota_persistent_email', email || '');

            if (email) {
                this.showQuickToast('默认邮箱已保存', 'success');
                getLogger().log(`默认邮箱已保存: ${email}`, 'info');
            } else {
                this.showQuickToast('默认邮箱已清除', 'info');
                getLogger().log('默认邮箱已清除', 'info');
            }

        } catch (error) {
            getLogger().log(`保存默认邮箱失败: ${error.message}`, 'error');
            this.showAlert('保存失败', 'error');
        }
    }

    /**
     * 加载持久化邮箱
     */
    loadPersistentEmail() {
        try {
            const savedEmail = localStorage.getItem('ota_persistent_email') || '';
            if (this.elements.persistentEmail) {
                this.elements.persistentEmail.value = savedEmail;
                this.validatePersistentEmail();
            }
        } catch (error) {
            getLogger().log(`加载默认邮箱失败: ${error.message}`, 'error');
        }
    }

    /**
     * 获取持久化邮箱
     * @returns {string} 邮箱地址
     */
    getPersistentEmail() {
        return localStorage.getItem('ota_persistent_email') || '';
    }

    /**
     * 自动填充客户邮箱（当AI解析无法获取时）
     */
    autoFillCustomerEmail() {
        const customerEmail = this.elements.customerEmail?.value.trim();
        const persistentEmail = this.getPersistentEmail();

        // 如果客户邮箱为空、N/A或无效，且有设置默认邮箱，则自动填充
        if ((!customerEmail || customerEmail === 'N/A' || customerEmail === 'null') && persistentEmail) {
            if (this.elements.customerEmail) {
                this.elements.customerEmail.value = persistentEmail;
                getLogger().log(`已自动填充客户邮箱: ${persistentEmail}`, 'info');
            }
        }
    }

    /**
     * 处理语言切换
     */
    handleLanguageToggle() {
        const i18n = getI18nManager();
        const currentLang = i18n.getCurrentLanguage();
        const newLang = currentLang === 'zh' ? 'en' : 'zh';

        i18n.setLanguage(newLang);

        // 更新语言切换按钮文本
        if (this.elements.languageToggle) {
            this.elements.languageToggle.textContent = newLang === 'zh' ? '中/EN' : 'EN/中';
        }

        getLogger().log(`语言已切换为: ${newLang}`, 'info');
    }

    /**
     * 初始化国际化
     */
    initializeI18n() {
        try {
            const i18n = getI18nManager();

            // 初始化界面文本
            i18n.updateUI();

            // 监听语言变化事件
            document.addEventListener('languageChanged', (e) => {
                const newLang = e.detail.language;

                // 更新语言切换按钮文本
                if (this.elements.languageToggle) {
                    this.elements.languageToggle.textContent = newLang === 'zh' ? '中/EN' : 'EN/中';
                }

                // 更新Gemini状态文本
                if (this.elements.geminiStatus) {
                    this.elements.geminiStatus.textContent = i18n.t('input.geminiStatus');
                }
            });

            getLogger().log('国际化已初始化', 'info');

        } catch (error) {
            getLogger().log(`国际化初始化失败: ${error.message}`, 'error');
        }
    }

    /**
     * 处理价格转换
     * @param {Array|Object} data - 订单数据（可能是数组或单个对象）
     */
    processPriceConversion(data) {
        try {
            if (!data) {
                getLogger().log('价格转换：数据为空，跳过处理', 'warning');
                return;
            }

            const currencyConverter = getCurrencyConverter();

            // 确保数据是数组格式
            const ordersArray = Array.isArray(data) ? data : [data];

            // 处理每个订单的价格转换
            ordersArray.forEach((orderData, index) => {
                if (orderData && orderData.price && orderData.currency) {
                    const conversionResult = currencyConverter.convertToMYR(orderData.price, orderData.currency);

                    // 更新订单数据
                    orderData.originalPrice = conversionResult.originalAmount;
                    orderData.originalCurrency = conversionResult.originalCurrency;
                    orderData.price = conversionResult.convertedAmount;
                    orderData.currency = conversionResult.convertedCurrency;
                    orderData.priceConversion = {
                        needsConversion: conversionResult.needsConversion,
                        exchangeRate: conversionResult.exchangeRate,
                        conversionNote: conversionResult.needsConversion
                            ? `原价 ${conversionResult.originalAmount} ${conversionResult.originalCurrency}，按汇率 ${conversionResult.exchangeRate} 转换`
                            : null
                    };

                    if (conversionResult.needsConversion) {
                        getLogger().log(`订单${index + 1}价格已转换: ${conversionResult.originalAmount} ${conversionResult.originalCurrency} → ${conversionResult.convertedAmount} MYR`, 'info');
                    }
                }
            });

        } catch (error) {
            getLogger().log(`价格转换处理失败: ${error.message}`, 'error');
        }
    }

    /**
     * 在预览界面显示价格转换信息
     * @param {Object} orderData - 订单数据
     */
    displayPriceConversionInfo(orderData) {
        const priceField = document.getElementById('otaPrice');
        if (!priceField || !orderData.priceConversion) {
            return;
        }

        // 创建价格转换提示
        let priceDisplay = orderData.price || '';

        if (orderData.priceConversion.needsConversion) {
            const originalPrice = `${orderData.originalPrice} ${orderData.originalCurrency}`;
            const convertedPrice = `${orderData.price} MYR`;
            const rate = orderData.priceConversion.exchangeRate;

            priceDisplay = `${orderData.price}`;

            // 添加转换提示到字段旁边
            const conversionNote = document.createElement('div');
            conversionNote.className = 'price-conversion-note';
            conversionNote.innerHTML = `
                <div class="conversion-info">
                    <span class="conversion-icon">💱</span>
                    <span class="conversion-text">
                        原价: ${originalPrice} → ${convertedPrice} (汇率: ${rate})
                    </span>
                </div>
            `;

            // 移除旧的转换提示
            const existingNote = priceField.parentNode.querySelector('.price-conversion-note');
            if (existingNote) {
                existingNote.remove();
            }

            // 添加新的转换提示
            priceField.parentNode.appendChild(conversionNote);
        }

        priceField.value = priceDisplay;
    }

    /**
     * 处理多订单模式
     * @param {Array|Object} data - 订单数据
     * @param {string} originalText - 原始文本
     */
    processMultiOrderMode(data, originalText) {
        try {
            if (!data || !originalText) {
                getLogger().log('多订单处理：数据或文本为空，跳过处理', 'warning');
                return;
            }

            const multiOrderManager = getMultiOrderManager();

            // 检测是否为多订单模式
            const isMultiOrder = multiOrderManager.isMultiOrderMode(originalText);

            getLogger().log(`多订单检测结果: ${isMultiOrder}, 数据类型: ${Array.isArray(data) ? 'array' : typeof data}, 数据长度: ${Array.isArray(data) ? data.length : 'N/A'}`, 'info');

            if (isMultiOrder) {
                let processedOrders;

                if (Array.isArray(data) && data.length > 1) {
                    // Gemini已经返回了多订单数组
                    processedOrders = multiOrderManager.processMultiOrderData(data, originalText);
                    getLogger().log('使用Gemini返回的多订单数据', 'info');
                } else {
                    // Gemini返回单个订单，但文本检测为多订单，需要手动分割
                    getLogger().log('文本检测为多订单，但Gemini返回单订单，尝试手动分割', 'info');
                    const segments = multiOrderManager.splitOrderText(originalText);

                    if (segments.length > 1) {
                        // 为每个分段创建订单数据
                        processedOrders = segments.map((segment, index) => {
                            const baseData = Array.isArray(data) ? data[0] : data;
                            return {
                                ...baseData,
                                order_sequence: index + 1,
                                ota_reference_number: multiOrderManager.generateSequentialReference(
                                    baseData.ota_reference_number || 'GMH-001',
                                    index + 1
                                ),
                                segment_text: segment
                            };
                        });

                        getLogger().log(`手动分割生成 ${processedOrders.length} 个订单`, 'info');
                    } else {
                        // 分割失败，降级为单订单模式
                        getLogger().log('订单分割失败，降级为单订单模式', 'warning');
                        getAppState().set('isMultiOrderMode', false);
                        getAppState().set('multiOrderStats', null);
                        return;
                    }
                }

                // 更新应用状态
                getAppState().set('currentOrder', processedOrders);
                getAppState().set('isMultiOrderMode', true);
                getAppState().set('multiOrderStats', multiOrderManager.getMultiOrderStats(processedOrders));

                // 显示多订单提示
                this.showMultiOrderNotification(processedOrders.length);

                getLogger().log(`多订单模式已启动，共 ${processedOrders.length} 个订单`, 'success');
            } else {
                // 单订单模式
                getAppState().set('isMultiOrderMode', false);
                getAppState().set('multiOrderStats', null);
                getLogger().log('单订单模式', 'info');
            }

        } catch (error) {
            getLogger().log(`多订单处理失败: ${error.message}`, 'error');
            // 降级为单订单模式
            getAppState().set('isMultiOrderMode', false);
            getAppState().set('multiOrderStats', null);
        }
    }

    /**
     * 显示多订单通知
     * @param {number} orderCount - 订单数量
     */
    showMultiOrderNotification(orderCount) {
        this.showQuickToast(`🔢 检测到 ${orderCount} 个订单，已启动多订单模式`, 'info', 4000);

        // 更新Gemini状态显示
        this.updateGeminiStatus(`✅ 多订单解析完成！共 ${orderCount} 个订单`);
    }

    /**
     * 检查是否为多订单模式
     * @returns {boolean} 是否为多订单模式
     */
    isCurrentlyMultiOrderMode() {
        return getAppState().get('isMultiOrderMode') || false;
    }

    /**
     * 获取当前多订单统计信息
     * @returns {Object|null} 多订单统计信息
     */
    getCurrentMultiOrderStats() {
        return getAppState().get('multiOrderStats') || null;
    }

    /**
     * 显示多订单预览面板
     */
    showMultiOrderPanel() {
        if (this.elements.multiOrderPanel) {
            this.elements.multiOrderPanel.classList.remove('hidden');
            this.elements.multiOrderPanel.classList.add('show');

            // 防止背景滚动
            document.body.style.overflow = 'hidden';

            // 加载多订单数据
            this.loadMultiOrderData();

            getLogger().log('多订单预览面板已显示', 'info');
        }
    }

    /**
     * 隐藏多订单预览面板
     */
    hideMultiOrderPanel() {
        if (this.elements.multiOrderPanel) {
            this.elements.multiOrderPanel.classList.add('hidden');
            this.elements.multiOrderPanel.classList.remove('show');

            // 恢复背景滚动
            document.body.style.overflow = '';

            getLogger().log('多订单预览面板已隐藏', 'info');
        }
    }

    /**
     * 加载多订单数据
     */
    loadMultiOrderData() {
        try {
            const ordersData = getAppState().get('currentOrder');
            const stats = this.getCurrentMultiOrderStats();

            if (!Array.isArray(ordersData) || !stats) {
                getLogger().log('无多订单数据可显示', 'warning');
                return;
            }

            // 更新统计信息
            this.updateMultiOrderStats(stats);

            // 渲染订单列表
            this.renderMultiOrderList(ordersData);

        } catch (error) {
            getLogger().log(`加载多订单数据失败: ${error.message}`, 'error');
        }
    }

    /**
     * 更新多订单统计信息
     * @param {Object} stats - 统计数据
     */
    updateMultiOrderStats(stats) {
        if (this.elements.multiOrderCount) {
            this.elements.multiOrderCount.textContent = `${stats.orderCount} 个订单`;
        }

        if (this.elements.multiOrderDateRange && stats.dateRange) {
            this.elements.multiOrderDateRange.textContent = stats.dateRange;
        }
    }

    /**
     * 渲染多订单列表
     * @param {Array} ordersData - 订单数据数组
     */
    renderMultiOrderList(ordersData) {
        if (!this.elements.multiOrderList) return;

        const listHTML = ordersData.map((orderData, index) =>
            this.createMultiOrderItemHTML(orderData, index)
        ).join('');

        this.elements.multiOrderList.innerHTML = listHTML;

        // 绑定订单项事件
        this.bindMultiOrderItemEvents();

        // 更新选中计数
        this.updateSelectedOrderCount();
    }

    /**
     * 创建多订单项HTML
     * @param {Object} orderData - 订单数据
     * @param {number} index - 索引
     * @returns {string} HTML字符串
     */
    createMultiOrderItemHTML(orderData, index) {
        const sequence = orderData.order_sequence || (index + 1);
        const reference = orderData.ota_reference_number || 'N/A';

        return `
            <div class="multi-order-item" data-order-index="${index}">
                <input type="checkbox" class="order-checkbox" checked>
                <div class="multi-order-item-header">
                    <div class="order-sequence">订单 ${sequence}</div>
                    <div class="order-actions">
                        <button type="button" class="btn btn-outline btn-sm edit-order-btn" data-order-index="${index}">编辑</button>
                        <button type="button" class="btn btn-outline btn-sm preview-order-btn" data-order-index="${index}">预览</button>
                    </div>
                </div>
                <div class="multi-order-item-content">
                    <div class="order-field">
                        <div class="order-field-label">参考号</div>
                        <div class="order-field-value">${reference}</div>
                    </div>
                    <div class="order-field">
                        <div class="order-field-label">客户姓名</div>
                        <div class="order-field-value ${orderData.customer_name ? '' : 'empty'}">${orderData.customer_name || '未填写'}</div>
                    </div>
                    <div class="order-field">
                        <div class="order-field-label">服务类型</div>
                        <div class="order-field-value">${this.getServiceTypeName(orderData.sub_category_id)}</div>
                    </div>
                    <div class="order-field">
                        <div class="order-field-label">日期时间</div>
                        <div class="order-field-value">${orderData.pickup_date || 'N/A'} ${orderData.pickup_time || ''}</div>
                    </div>
                    <div class="order-field">
                        <div class="order-field-label">上车地点</div>
                        <div class="order-field-value ${orderData.pickup ? '' : 'empty'}">${orderData.pickup || '未填写'}</div>
                    </div>
                    <div class="order-field">
                        <div class="order-field-label">目的地</div>
                        <div class="order-field-value ${orderData.dropoff ? '' : 'empty'}">${orderData.dropoff || '未填写'}</div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 获取服务类型名称
     * @param {number} subCategoryId - 子分类ID
     * @returns {string} 服务类型名称
     */
    getServiceTypeName(subCategoryId) {
        const serviceTypes = {
            2: '接机',
            3: '送机',
            4: '包车'
        };
        return serviceTypes[subCategoryId] || '未知';
    }

    /**
     * 绑定多订单项事件
     */
    bindMultiOrderItemEvents() {
        // 复选框变化事件
        document.querySelectorAll('.order-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', () => this.updateSelectedOrderCount());
        });

        // 编辑按钮事件
        document.querySelectorAll('.edit-order-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const orderIndex = parseInt(e.target.getAttribute('data-order-index'));
                this.editSingleOrder(orderIndex);
            });
        });

        // 预览按钮事件
        document.querySelectorAll('.preview-order-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const orderIndex = parseInt(e.target.getAttribute('data-order-index'));
                this.previewSingleOrder(orderIndex);
            });
        });
    }

    /**
     * 更新选中订单计数
     */
    updateSelectedOrderCount() {
        const checkboxes = document.querySelectorAll('.order-checkbox');
        const selectedCount = Array.from(checkboxes).filter(cb => cb.checked).length;

        if (this.elements.selectedOrderCount) {
            this.elements.selectedOrderCount.textContent = `已选择 ${selectedCount} 个订单`;
        }
    }

    /**
     * 全选订单
     */
    selectAllOrders() {
        document.querySelectorAll('.order-checkbox').forEach(checkbox => {
            checkbox.checked = true;
        });
        this.updateSelectedOrderCount();
    }

    /**
     * 取消全选
     */
    deselectAllOrders() {
        document.querySelectorAll('.order-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
        this.updateSelectedOrderCount();
    }

    /**
     * 验证所有订单
     */
    validateAllOrders() {
        // TODO: 实现订单验证逻辑
        this.showQuickToast('订单验证功能开发中', 'info');
    }

    /**
     * 批量创建订单
     */
    handleBatchCreate() {
        this.createSelectedOrders();
    }

    /**
     * 创建选中的订单
     */
    createSelectedOrders() {
        // TODO: 实现批量创建逻辑
        this.showQuickToast('批量创建功能开发中', 'info');
    }

    /**
     * 编辑单个订单
     * @param {number} orderIndex - 订单索引
     */
    editSingleOrder(orderIndex) {
        // TODO: 实现单个订单编辑
        this.showQuickToast(`编辑订单 ${orderIndex + 1} 功能开发中`, 'info');
    }

    /**
     * 预览单个订单
     * @param {number} orderIndex - 订单索引
     */
    previewSingleOrder(orderIndex) {
        // TODO: 实现单个订单预览
        this.showQuickToast(`预览订单 ${orderIndex + 1} 功能开发中`, 'info');
    }

    /**
     * 处理举牌服务
     * @param {Array|Object} data - 订单数据
     * @param {string} originalText - 原始文本
     */
    processPagingService(data, originalText) {
        try {
            if (!data || !originalText) {
                getLogger().log('举牌服务处理：数据或文本为空，跳过处理', 'warning');
                return;
            }

            const pagingServiceManager = getPagingServiceManager();

            // 检测是否需要举牌服务
            const needsPaging = pagingServiceManager.detectPagingService(originalText);

            if (needsPaging) {
                let processedData;

                if (Array.isArray(data)) {
                    // 多订单模式：处理举牌服务并可能生成举牌订单
                    processedData = pagingServiceManager.processMultiOrderPagingService(data, originalText);
                } else {
                    // 单订单模式：标记需要举牌服务
                    processedData = pagingServiceManager.processOrdersForPagingService(data, originalText);
                }

                // 更新应用状态
                getAppState().set('currentOrder', processedData);

                // 获取举牌服务统计
                const pagingStats = pagingServiceManager.getPagingServiceStats(
                    Array.isArray(processedData) ? processedData : [processedData]
                );

                getAppState().set('pagingServiceStats', pagingStats);

                // 显示举牌服务提示
                this.showPagingServiceNotification(pagingStats);

                getLogger().log('举牌服务处理完成', 'info');
            } else {
                getAppState().set('pagingServiceStats', null);
            }

        } catch (error) {
            getLogger().log(`举牌服务处理失败: ${error.message}`, 'error');
        }
    }

    /**
     * 显示举牌服务通知
     * @param {Object} pagingStats - 举牌服务统计
     */
    showPagingServiceNotification(pagingStats) {
        if (!pagingStats.hasPagingService) return;

        const pagingServiceManager = getPagingServiceManager();
        const summary = pagingServiceManager.createPagingServiceSummary(pagingStats);

        this.showQuickToast(`🎫 检测到举牌服务：${summary}`, 'info', 4000);

        // 更新Gemini状态显示
        if (pagingStats.pagingOrderCount > 0) {
            this.updateGeminiStatus(`✅ 已自动生成 ${pagingStats.pagingOrderCount} 个举牌订单`);
        }
    }

    /**
     * 获取当前举牌服务统计
     * @returns {Object|null} 举牌服务统计
     */
    getCurrentPagingServiceStats() {
        return getAppState().get('pagingServiceStats') || null;
    }

}

// 创建全局UI管理器实例
const uiManager = new UIManager();

// 暴露到OTA命名空间
window.OTA.uiManager = uiManager;

// 向后兼容：暴露到全局window对象
window.uiManager = uiManager;

})();