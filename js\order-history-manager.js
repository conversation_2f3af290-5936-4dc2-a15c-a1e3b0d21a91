/**
 * 历史订单管理器
 * 负责订单历史记录的存储、检索和管理
 * <AUTHOR>
 * @version 1.0.0
 */

class OrderHistoryManager {
    constructor() {
        this.storageKey = 'ota_order_history';
        this.maxHistorySize = 1000; // 最大存储1000条历史记录
        this.init();
    }

    /**
     * 初始化历史订单管理器
     */
    init() {
        // 确保localStorage中有历史记录数组
        if (!localStorage.getItem(this.storageKey)) {
            localStorage.setItem(this.storageKey, JSON.stringify([]));
        }
        
        getLogger().log('历史订单管理器已初始化', 'info');
    }

    /**
     * 添加新的订单记录
     * @param {Object} orderData - 订单数据
     * @param {string} orderId - GoMyHire API返回的订单ID
     * @param {Object} apiResponse - 完整的API响应
     */
    addOrder(orderData, orderId, apiResponse = null) {
        try {
            const historyRecord = {
                // 基础信息
                id: this.generateHistoryId(),
                orderId: orderId,
                timestamp: new Date().toISOString(),
                
                // 订单内容
                orderData: {
                    // 基本信息
                    subCategoryId: orderData.subCategoryId || '',
                    otaReferenceNumber: orderData.otaReferenceNumber || '',
                    otaChannel: orderData.otaChannel || '',
                    carTypeId: orderData.carTypeId || '',
                    
                    // 客户信息
                    customerName: orderData.customerName || '',
                    customerContact: orderData.customerContact || '',
                    customerEmail: orderData.customerEmail || '',
                    flightInfo: orderData.flightInfo || '',
                    
                    // 行程信息
                    pickup: orderData.pickup || '',
                    destination: orderData.destination || '',
                    date: orderData.date || '',
                    time: orderData.time || '',
                    passengerNumber: orderData.passengerNumber || '',
                    luggageNumber: orderData.luggageNumber || '',
                    
                    // 其他信息
                    specialRequests: orderData.specialRequests || '',
                    otaPrice: orderData.otaPrice || '',
                    drivingRegionId: orderData.drivingRegionId || '',
                    languagesIdArray: orderData.languagesIdArray || {}
                },
                
                // 元数据
                metadata: {
                    userEmail: getAppState().get('auth.user.email') || '',
                    createdBy: getAppState().get('auth.user.name') || '',
                    apiResponse: apiResponse ? {
                        success: apiResponse.success,
                        message: apiResponse.message,
                        data: apiResponse.data
                    } : null
                }
            };

            // 获取现有历史记录
            const history = this.getHistory();
            
            // 添加新记录到开头
            history.unshift(historyRecord);
            
            // 限制历史记录数量
            if (history.length > this.maxHistorySize) {
                history.splice(this.maxHistorySize);
            }
            
            // 保存到localStorage
            localStorage.setItem(this.storageKey, JSON.stringify(history));
            
            getLogger().log(`订单历史记录已添加: ${orderId}`, 'success');
            
            return historyRecord;
            
        } catch (error) {
            getLogger().log(`添加订单历史记录失败: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * 获取所有历史订单
     * @returns {Array} 历史订单数组
     */
    getHistory() {
        try {
            const historyJson = localStorage.getItem(this.storageKey);
            return historyJson ? JSON.parse(historyJson) : [];
        } catch (error) {
            getLogger().log(`获取历史订单失败: ${error.message}`, 'error');
            return [];
        }
    }

    /**
     * 根据条件搜索历史订单
     * @param {Object} criteria - 搜索条件
     * @returns {Array} 匹配的订单数组
     */
    searchOrders(criteria = {}) {
        const history = this.getHistory();
        
        return history.filter(record => {
            // 按订单ID搜索
            if (criteria.orderId && !record.orderId.toLowerCase().includes(criteria.orderId.toLowerCase())) {
                return false;
            }
            
            // 按OTA参考号搜索
            if (criteria.otaReference && !record.orderData.otaReferenceNumber.toLowerCase().includes(criteria.otaReference.toLowerCase())) {
                return false;
            }
            
            // 按客户姓名搜索
            if (criteria.customerName && !record.orderData.customerName.toLowerCase().includes(criteria.customerName.toLowerCase())) {
                return false;
            }
            
            // 按客户邮箱搜索
            if (criteria.customerEmail && !record.orderData.customerEmail.toLowerCase().includes(criteria.customerEmail.toLowerCase())) {
                return false;
            }
            
            // 按日期范围搜索
            if (criteria.dateFrom) {
                const recordDate = new Date(record.timestamp);
                const fromDate = new Date(criteria.dateFrom);
                if (recordDate < fromDate) return false;
            }
            
            if (criteria.dateTo) {
                const recordDate = new Date(record.timestamp);
                const toDate = new Date(criteria.dateTo);
                toDate.setHours(23, 59, 59, 999); // 包含整天
                if (recordDate > toDate) return false;
            }
            
            return true;
        });
    }

    /**
     * 根据ID获取特定订单
     * @param {string} historyId - 历史记录ID
     * @returns {Object|null} 订单记录
     */
    getOrderById(historyId) {
        const history = this.getHistory();
        return history.find(record => record.id === historyId) || null;
    }

    /**
     * 删除历史订单
     * @param {string} historyId - 历史记录ID
     * @returns {boolean} 是否删除成功
     */
    deleteOrder(historyId) {
        try {
            const history = this.getHistory();
            const index = history.findIndex(record => record.id === historyId);
            
            if (index === -1) {
                return false;
            }
            
            history.splice(index, 1);
            localStorage.setItem(this.storageKey, JSON.stringify(history));
            
            getLogger().log(`历史订单已删除: ${historyId}`, 'info');
            return true;
            
        } catch (error) {
            getLogger().log(`删除历史订单失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 清空所有历史记录
     * @returns {boolean} 是否清空成功
     */
    clearHistory() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify([]));
            getLogger().log('所有历史订单已清空', 'info');
            return true;
        } catch (error) {
            getLogger().log(`清空历史订单失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 导出历史订单数据
     * @param {Array} orders - 要导出的订单数组（可选，默认导出所有）
     * @param {string} format - 导出格式 ('json' | 'csv')
     * @returns {string} 导出的数据字符串
     */
    exportOrders(orders = null, format = 'json') {
        const ordersToExport = orders || this.getHistory();
        
        if (format === 'csv') {
            return this.exportToCSV(ordersToExport);
        } else {
            return JSON.stringify(ordersToExport, null, 2);
        }
    }

    /**
     * 导出为CSV格式
     * @param {Array} orders - 订单数组
     * @returns {string} CSV字符串
     */
    exportToCSV(orders) {
        if (orders.length === 0) return '';
        
        // CSV头部
        const headers = [
            '订单ID', 'OTA参考号', '创建时间', '客户姓名', '客户邮箱', '客户电话',
            '上车地点', '目的地', '日期', '时间', '乘客人数', '行李数量',
            '航班信息', 'OTA渠道', '特殊要求', '创建者'
        ];
        
        // CSV数据行
        const rows = orders.map(record => [
            record.orderId,
            record.orderData.otaReferenceNumber,
            new Date(record.timestamp).toLocaleString('zh-CN'),
            record.orderData.customerName,
            record.orderData.customerEmail,
            record.orderData.customerContact,
            record.orderData.pickup,
            record.orderData.destination,
            record.orderData.date,
            record.orderData.time,
            record.orderData.passengerNumber,
            record.orderData.luggageNumber,
            record.orderData.flightInfo,
            record.orderData.otaChannel,
            record.orderData.specialRequests,
            record.metadata.createdBy
        ]);
        
        // 组合CSV
        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${(field || '').toString().replace(/"/g, '""')}"`).join(','))
            .join('\n');
            
        return csvContent;
    }

    /**
     * 生成唯一的历史记录ID
     * @returns {string} 唯一ID
     */
    generateHistoryId() {
        return 'hist_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 获取统计信息
     * @returns {Object} 统计数据
     */
    getStatistics() {
        const history = this.getHistory();
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        
        return {
            total: history.length,
            today: history.filter(r => new Date(r.timestamp) >= today).length,
            thisWeek: history.filter(r => new Date(r.timestamp) >= thisWeek).length,
            thisMonth: history.filter(r => new Date(r.timestamp) >= thisMonth).length,
            oldestRecord: history.length > 0 ? history[history.length - 1].timestamp : null,
            newestRecord: history.length > 0 ? history[0].timestamp : null
        };
    }
}

// 创建全局实例
let orderHistoryManagerInstance = null;

/**
 * 获取历史订单管理器实例
 * @returns {OrderHistoryManager} 管理器实例
 */
function getOrderHistoryManager() {
    if (!orderHistoryManagerInstance) {
        orderHistoryManagerInstance = new OrderHistoryManager();
    }
    return orderHistoryManagerInstance;
}

// 导出到全局作用域
window.OrderHistoryManager = OrderHistoryManager;
window.getOrderHistoryManager = getOrderHistoryManager;
