/**
 * OTA订单处理系统 - 主入口文件
 * 负责系统初始化和启动
 * 重构为传统script标签加载方式
 */

// 等待所有模块加载完成后初始化应用
document.addEventListener('DOMContentLoaded', function() {
    // 确保所有依赖模块已加载
    if (!window.OTA || !window.OTA.appState || !window.OTA.apiService ||
        !window.OTA.geminiService || !window.OTA.uiManager ||
        !window.OTA.logger || !window.OTA.utils) {
        console.error('部分模块未正确加载，请检查script标签顺序');
        return;
    }

    // 获取模块引用
    const appState = window.OTA.appState;
    const apiService = window.OTA.apiService;
    const geminiService = window.OTA.geminiService;
    const uiManager = window.OTA.uiManager;
    const logger = window.OTA.logger;
    const utils = window.OTA.utils;

/**
 * 应用程序类
 */
class OTAApplication {
    constructor() {
        this.isInitialized = false;
        this.startTime = Date.now();
        this.version = '1.0.0';
        
        // 绑定方法上下文
        this.handleUnload = this.handleUnload.bind(this);
        this.handleError = this.handleError.bind(this);
        this.handleUnhandledRejection = this.handleUnhandledRejection.bind(this);
    }
    
    /**
     * 初始化应用程序
     */
    async init() {
        // 补丁：确保全局 GeminiService 实例包含 setRealtimeAnalysis 方法
        if (window.OTA && window.OTA.geminiService && typeof window.OTA.geminiService.setRealtimeAnalysis !== 'function') {
            window.OTA.geminiService.setRealtimeAnalysis = function(config) {
                // 如果传入值为 boolean，则调整为配置对象
                if (typeof config === 'boolean') {
                    this.configureRealtimeAnalysis({ enabled: config });
                } else {
                    this.configureRealtimeAnalysis(config);
                }
            };
            console.log('Patched geminiService.setRealtimeAnalysis');
        }
        try {
            logger.log('开始初始化OTA订单处理系统', 'info', {
                version: this.version,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString()
            });
            
            // 设置性能监控
            utils.performanceMonitor.mark('app-init');
            
            // 检查浏览器兼容性
            this.checkBrowserCompatibility();
            
            // 设置错误处理
            this.setupErrorHandling();
            
            // 设置环境变量（如果有）
            this.loadEnvironmentVariables();
            
            // 初始化各个模块
            await this.initializeModules();
            
            // 设置系统事件监听
            this.setupEventListeners();
            
            // 标记初始化完成
            this.isInitialized = true;
            
            // 启动token自动检查
            this.setupTokenRefresh();
            
            const initTime = utils.performanceMonitor.measure('app-init');
            logger.log('OTA订单处理系统初始化完成', 'success', {
                initTime: `${initTime.toFixed(2)}ms`,
                modules: ['appState', 'apiService', 'geminiService', 'uiManager', 'logger']
            });
            
            // 显示系统信息
            this.displaySystemInfo();
            
        } catch (error) {
            logger.logError('系统初始化失败', error);
            this.showInitializationError(error);
        }
    }
    
    /**
     * 检查浏览器兼容性
     */
    checkBrowserCompatibility() {
        const browserInfo = utils.getBrowserInfo();
        const requiredFeatures = [
            'fetch',
            'localStorage',
            'Promise',
            'URLSearchParams'
        ];
        
        const missingFeatures = requiredFeatures.filter(feature => {
            switch (feature) {
                case 'fetch':
                    return !window.fetch;
                case 'localStorage':
                    return !window.localStorage;
                case 'Promise':
                    return !window.Promise;
                case 'URLSearchParams':
                    return !window.URLSearchParams;
                default:
                    return false;
            }
        });
        
        if (missingFeatures.length > 0) {
            const message = `浏览器不支持以下功能: ${missingFeatures.join(', ')}`;
            logger.logError('浏览器兼容性检查失败', { 
                browserInfo, 
                missingFeatures 
            });
            throw new Error(message);
        }
        
        logger.log('浏览器兼容性检查通过', 'success', browserInfo);
    }
    
    /**
     * 设置错误处理
     */
    setupErrorHandling() {
        // 全局错误处理
        window.addEventListener('error', this.handleError);
        
        // Promise错误处理
        window.addEventListener('unhandledrejection', this.handleUnhandledRejection);
        
        // 页面卸载处理
        window.addEventListener('beforeunload', this.handleUnload);
        
        logger.log('错误处理机制已设置', 'info');
    }
    
    /**
     * 加载环境变量
     */
    loadEnvironmentVariables() {
        // 尝试从meta标签获取配置
        const configMeta = document.querySelector('meta[name="ota-config"]');
        if (configMeta) {
            try {
                const config = JSON.parse(configMeta.content);
                Object.entries(config).forEach(([key, value]) => {
                    appState.set(`config.${key}`, value, false);
                });
                logger.log('环境配置已加载', 'info', config);
            } catch (error) {
                logger.logError('环境配置解析失败', error);
            }
        }
        
        // 检查URL参数中的配置
        const urlParams = utils.parseUrlParams();
        if (urlParams.debug === 'true') {
            appState.setDebugMode(true);
            logger.log('调试模式已启用（URL参数）', 'info');
        }
        
        if (urlParams.theme) {
            appState.setTheme(urlParams.theme);
            logger.log(`主题已设置为: ${urlParams.theme}`, 'info');
        }
    }
    
    /**
     * 初始化各个模块
     */
    async initializeModules() {
        logger.log('开始初始化模块', 'info');
        
        // 初始化UI管理器
        utils.performanceMonitor.mark('ui-init');
        uiManager.init();
        const uiInitTime = utils.performanceMonitor.measure('ui-init');
        logger.log('UI管理器初始化完成', 'success', { 
            time: `${uiInitTime.toFixed(2)}ms` 
        });
        
        // 恢复登录状态（如果有有效的token）
        await this.restoreLoginState();
        
        // 如果用户已登录，尝试获取系统数据
        if (appState.get('auth.isLoggedIn')) {
            try {
                utils.performanceMonitor.mark('system-data-load');
                
                // 检查缓存的系统数据是否过期
                const lastUpdated = appState.get('systemData.lastUpdated');
                const needsRefresh = !lastUpdated || 
                    (Date.now() - new Date(lastUpdated).getTime()) > 24 * 60 * 60 * 1000;
                
                if (needsRefresh) {
                    logger.log('系统数据已过期，重新获取', 'info');
                    await apiService.getAllSystemData();
                } else {
                    logger.log('使用缓存的系统数据', 'info');
                }
                
                const dataLoadTime = utils.performanceMonitor.measure('system-data-load');
                logger.log('系统数据加载完成', 'success', { 
                    time: `${dataLoadTime.toFixed(2)}ms`,
                    fromCache: !needsRefresh
                });
                
            } catch (error) {
                logger.logError('系统数据加载失败', error);
                // 不阻断初始化，使用静态数据
            }
        }
        
        // 检查Gemini API可用性（已内嵌配置）
        if (geminiService.isAvailable()) {
            logger.log('Gemini AI服务可用', 'success', {
                model: geminiService.modelVersion,
                apiKeyConfigured: true
            });
        } else {
            logger.log('Gemini AI服务配置异常', 'error');
        }
    }
    
    /**
     * 设置系统事件监听
     */
    setupEventListeners() {
        // 网络状态监听
        window.addEventListener('online', () => {
            logger.log('网络连接已恢复', 'success');
            appState.set('system.connected', true);
        });
        
        window.addEventListener('offline', () => {
            logger.log('网络连接已断开', 'warning');
            appState.set('system.connected', false);
        });
        
        // 可见性变化监听
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                logger.logUserAction('页面重新激活');
                // 检查是否需要刷新数据
                this.checkDataFreshness();
            } else {
                logger.logUserAction('页面进入后台');
            }
        });
        
        // 窗口大小变化监听
        const resizeHandler = utils.debounce(() => {
            const viewport = {
                width: window.innerWidth,
                height: window.innerHeight,
                isMobile: utils.isMobile()
            };
            logger.logUserAction('窗口大小变化', viewport);
        }, 500);
        
        window.addEventListener('resize', resizeHandler);
        
        logger.log('系统事件监听已设置', 'info');
    }
    
    /**
     * 检查数据新鲜度
     */
    async checkDataFreshness() {
        if (!appState.get('auth.isLoggedIn')) return;
        
        const lastUpdated = appState.get('systemData.lastUpdated');
        if (!lastUpdated) return;
        
        const hoursAgo = (Date.now() - new Date(lastUpdated).getTime()) / (1000 * 60 * 60);
        
        if (hoursAgo > 4) { // 4小时后检查更新
            try {
                logger.log('检查系统数据更新', 'info');
                await apiService.getAllSystemData();
                logger.log('系统数据已更新', 'success');
            } catch (error) {
                logger.logError('系统数据更新失败', error);
            }
        }
    }
    
    /**
     * 设置token自动刷新
     * 在token即将过期前尝试刷新
     */
    setupTokenRefresh() {
        const checkInterval = 30 * 60 * 1000; // 每30分钟检查一次
        
        setInterval(() => {
            const isLoggedIn = appState.get('auth.isLoggedIn');
            const tokenExpiry = appState.get('auth.tokenExpiry');
            const rememberMe = appState.get('auth.rememberMe');
            
            if (!isLoggedIn || !tokenExpiry) return;
            
            const expiryDate = new Date(tokenExpiry);
            const now = new Date();
            const timeUntilExpiry = expiryDate - now;
            const hoursUntilExpiry = timeUntilExpiry / (1000 * 60 * 60);
            
            // 如果token在1小时内过期且用户选择了记住登录
            if (hoursUntilExpiry < 1 && hoursUntilExpiry > 0 && rememberMe) {
                logger.log('Token即将过期，提醒用户', 'warning', {
                    expiresIn: `${Math.floor(hoursUntilExpiry * 60)}分钟`
                });
                
                uiManager.showAlert('登录状态即将过期，请重新登录以继续使用', 'warning', 10000);
            } else if (timeUntilExpiry <= 0) {
                // Token已过期
                logger.log('Token已过期，清除登录状态', 'info');
                appState.clearAuth();
                uiManager.updateLoginUI(false);
                uiManager.showAlert('登录状态已过期，请重新登录', 'info');
            }
        }, checkInterval);
        
        logger.log('Token自动检查已启动', 'info', { checkInterval: '30分钟' });
    }

    /**
     * 恢复登录状态
     * 检查是否有有效的token并尝试自动登录
     */
    async restoreLoginState() {
        try {
            const isLoggedIn = appState.get('auth.isLoggedIn');
            const token = appState.get('auth.token');
            const tokenExpiry = appState.get('auth.tokenExpiry');
            const rememberMe = appState.get('auth.rememberMe');
            
            if (!isLoggedIn || !token || !tokenExpiry) {
                logger.log('没有有效的登录状态可恢复', 'info');
                return;
            }
            
            // 检查token是否过期
            const expiryDate = new Date(tokenExpiry);
            const now = new Date();
            
            if (expiryDate <= now) {
                logger.log('Token已过期，清除登录状态', 'info');
                appState.clearAuth(rememberMe);
                return;
            }
            
            // Token有效，确认登录状态
            logger.log('恢复登录状态成功', 'success', {
                tokenExpiry: expiryDate.toISOString(),
                timeUntilExpiry: Math.round((expiryDate - now) / (1000 * 60 * 60)) + '小时',
                rememberMe
            });
            
            // 更新UI状态
            uiManager.updateLoginUI(true);
            
        } catch (error) {
            logger.logError('恢复登录状态失败', error);
            appState.clearAuth();
        }
    }

    /**
     * 处理全局错误
     * @param {ErrorEvent} event - 错误事件
     */
    handleError(event) {
        const error = {
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            stack: event.error?.stack
        };
        
        logger.logError('全局JavaScript错误', error);
        
        // 如果是关键错误，显示用户友好的错误信息
        if (this.isCriticalError(event.error)) {
            this.showCriticalError(event.error);
        }
    }
    
    /**
     * 处理未捕获的Promise拒绝
     * @param {PromiseRejectionEvent} event - Promise拒绝事件
     */
    handleUnhandledRejection(event) {
        const error = {
            reason: event.reason,
            promise: event.promise
        };
        
        logger.logError('未捕获的Promise拒绝', error);
        
        // 阻止默认的错误显示
        event.preventDefault();
    }
    
    /**
     * 处理页面卸载
     * @param {BeforeUnloadEvent} event - 卸载事件
     */
    handleUnload(event) {
        // 检查是否有未保存的数据
        const hasUnsavedData = this.checkUnsavedData();
        
        if (hasUnsavedData) {
            const message = '您有未保存的数据，确定要离开吗？';
            event.returnValue = message;
            return message;
        }
        
        // 记录会话结束
        const sessionDuration = Date.now() - this.startTime;
        logger.logUserAction('会话结束', {
            duration: `${(sessionDuration / 1000).toFixed(1)}秒`,
            timestamp: new Date().toISOString()
        });
    }
    
    /**
     * 检查是否有未保存的数据
     * @returns {boolean} 是否有未保存的数据
     */
    checkUnsavedData() {
        const currentOrder = appState.get('currentOrder');
        
        // 检查是否有正在编辑的订单
        if (currentOrder && currentOrder.status === 'editing') {
            return true;
        }
        
        // 检查表单是否有内容
        const orderInput = document.getElementById('orderInput');
        if (orderInput && orderInput.value.trim()) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 判断是否为关键错误
     * @param {Error} error - 错误对象
     * @returns {boolean} 是否为关键错误
     */
    isCriticalError(error) {
        if (!error) return false;
        
        const criticalErrorPatterns = [
            /memory/i,
            /network/i,
            /security/i,
            /permission/i
        ];
        
        return criticalErrorPatterns.some(pattern => 
            pattern.test(error.message) || pattern.test(error.name)
        );
    }
    
    /**
     * 显示初始化错误
     * @param {Error} error - 错误对象
     */
    showInitializationError(error) {
        const errorContainer = document.createElement('div');
        errorContainer.className = 'init-error';
        errorContainer.innerHTML = `
            <div class="error-content">
                <h2>🚨 系统初始化失败</h2>
                <p><strong>错误信息:</strong> ${error.message}</p>
                <p>请尝试以下解决方案：</p>
                <ul>
                    <li>刷新页面重试</li>
                    <li>清除浏览器缓存</li>
                    <li>检查网络连接</li>
                    <li>更新浏览器版本</li>
                </ul>
                <button onclick="location.reload()" class="btn btn-primary">
                    重新加载
                </button>
            </div>
        `;
        
        errorContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
        `;
        
        document.body.appendChild(errorContainer);
    }
    
    /**
     * 显示关键错误
     * @param {Error} error - 错误对象
     */
    showCriticalError(error) {
        // 创建错误提示
        const errorAlert = document.createElement('div');
        errorAlert.className = 'critical-error-alert';
        errorAlert.innerHTML = `
            <div class="alert alert-error">
                <span class="alert-icon">⚠️</span>
                <span class="alert-message">发生严重错误，请刷新页面重试</span>
                <button class="alert-action" onclick="location.reload()">刷新</button>
            </div>
        `;
        
        document.body.appendChild(errorAlert);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (errorAlert.parentNode) {
                errorAlert.parentNode.removeChild(errorAlert);
            }
        }, 3000);
    }
    
    /**
     * 显示系统信息
     */
    displaySystemInfo() {
        const systemInfo = {
            version: this.version,
            initTime: `${Date.now() - this.startTime}ms`,
            browser: utils.getBrowserInfo(),
            features: {
                geminiAI: geminiService.isAvailable(),
                localStorage: !!window.localStorage,
                clipboard: !!navigator.clipboard
            },
            performance: utils.performanceMonitor.getStats()
        };
        
        logger.log('系统信息', 'info', systemInfo);
        
        // 在调试模式下显示到控制台
        if (appState.get('config.debugMode')) {
            console.group('🚗 OTA订单处理系统');
            console.log('版本:', this.version);
            console.log('初始化时间:', systemInfo.initTime);
            console.log('浏览器:', systemInfo.browser);
            console.log('功能支持:', systemInfo.features);
            console.groupEnd();
        }
    }
    
    /**
     * 获取应用状态
     * @returns {object} 应用状态信息
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            version: this.version,
            startTime: this.startTime,
            uptime: Date.now() - this.startTime,
            modules: {
                appState: !!window.appState,
                apiService: !!window.apiService,
                geminiService: !!window.geminiService,
                uiManager: !!window.uiManager,
                logger: !!window.logger
            }
        };
    }
    
    /**
     * 重启应用
     */
    async restart() {
        logger.log('重启应用程序', 'info');
        
        try {
            // 清理现有状态
            this.cleanup();
            
            // 重新初始化
            await this.init();
            
            logger.log('应用程序重启完成', 'success');
        } catch (error) {
            logger.logError('应用程序重启失败', error);
            throw error;
        }
    }
    
    /**
     * 清理应用资源
     */
    cleanup() {
        // 移除事件监听器
        window.removeEventListener('error', this.handleError);
        window.removeEventListener('unhandledrejection', this.handleUnhandledRejection);
        window.removeEventListener('beforeunload', this.handleUnload);
        
        // 清理性能监控
        utils.performanceMonitor.clear();
        
        this.isInitialized = false;
        
        logger.log('应用程序清理完成', 'info');
        }
    }

    // 创建全局应用实例
    const app = new OTAApplication();

    // 暴露到全局作用域（用于调试和向后兼容）
    window.app = app;

    // 暴露到OTA命名空间
    window.OTA.app = app;

    // 初始化应用
    app.init();

    logger.log('🎉 OTA订单处理系统初始化完成！', 'success', {
        modules: Object.keys(window.OTA),
        timestamp: new Date().toISOString()
    });

    logger.log('✅ 进阶功能列表：', 'info');
    logger.log('  - 📷 图片上传分析（Gemini Vision API集成）', 'info');
    logger.log('  - 💱 价格识别与货币转换（MYR/RMB/USD自动转换）', 'info');
    logger.log('  - 🔢 多订单模式（多日期检测和批量创建）', 'info');
    logger.log('  - 🎫 举牌服务自动识别（独立订单生成）', 'info');
    logger.log('  - 🌐 完整国际化支持（中英文切换）', 'info');
    logger.log('  - 🎨 Windows Fluent Design毛玻璃风格', 'info');

    // 初始化新功能管理器
    try {
        window.OTA.imageUploadManager = getImageUploadManager();
        window.OTA.currencyConverter = getCurrencyConverter();
        window.OTA.multiOrderManager = getMultiOrderManager();
        window.OTA.pagingServiceManager = getPagingServiceManager();

        logger.log('🔧 所有进阶功能管理器已初始化', 'success');
    } catch (error) {
        logger.log(`⚠️ 功能管理器初始化警告: ${error.message}`, 'warning');
    }

    // 系统健康检查
    performSystemHealthCheck();

});

/**
 * 执行系统健康检查
 */
function performSystemHealthCheck() {
    const logger = window.OTA.logger;
    logger.log('🔍 开始系统健康检查...', 'info');

    const checks = [
        // 核心服务检查
        { name: 'Logger服务', check: () => window.OTA && window.OTA.logger },
        { name: 'AppState服务', check: () => typeof getAppState === 'function' },
        { name: 'Gemini服务', check: () => typeof getGeminiService === 'function' },
        { name: 'API服务', check: () => typeof getAPIService === 'function' },
        { name: 'UI管理器', check: () => window.OTA && window.OTA.uiManager },

        // 进阶功能检查
        { name: '图片上传管理器', check: () => typeof getImageUploadManager === 'function' },
        { name: '货币转换器', check: () => typeof getCurrencyConverter === 'function' },
        { name: '多订单管理器', check: () => typeof getMultiOrderManager === 'function' },
        { name: '举牌服务管理器', check: () => typeof getPagingServiceManager === 'function' },
        { name: '历史订单管理器', check: () => typeof getOrderHistoryManager === 'function' },
        { name: '国际化管理器', check: () => typeof getI18nManager === 'function' },

        // DOM元素检查
        { name: '主要DOM元素', check: () => {
            const required = ['orderInput', 'orderForm', 'loginPanel', 'workspace'];
            return required.every(id => document.getElementById(id));
        }},

        // 本地存储检查
        { name: 'LocalStorage可用性', check: () => {
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                return true;
            } catch (e) {
                return false;
            }
        }}
    ];

    let passedChecks = 0;
    const totalChecks = checks.length;

    checks.forEach(({ name, check }) => {
        try {
            if (check()) {
                logger.log(`  ✅ ${name}`, 'info');
                passedChecks++;
            } else {
                logger.log(`  ❌ ${name}`, 'error');
            }
        } catch (error) {
            logger.log(`  ⚠️ ${name}: ${error.message}`, 'warning');
        }
    });

    const healthScore = Math.round((passedChecks / totalChecks) * 100);

    if (healthScore >= 90) {
        logger.log(`🎉 系统健康检查完成！健康度: ${healthScore}% (${passedChecks}/${totalChecks})`, 'success');
    } else if (healthScore >= 70) {
        logger.log(`⚠️ 系统健康检查完成，存在一些问题。健康度: ${healthScore}% (${passedChecks}/${totalChecks})`, 'warning');
    } else {
        logger.log(`❌ 系统健康检查发现严重问题！健康度: ${healthScore}% (${passedChecks}/${totalChecks})`, 'error');
    }

    // 性能检查
    if (performance && performance.now) {
        const loadTime = Math.round(performance.now());
        logger.log(`⚡ 系统加载时间: ${loadTime}ms`, loadTime < 1000 ? 'success' : 'warning');
    }
}