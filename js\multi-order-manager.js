/**
 * 多订单处理管理器
 * 负责多日期检测、订单智能分割和批量处理
 * <AUTHOR>
 * @version 1.0.0
 */

// 获取依赖模块（延迟获取以确保加载顺序）
function getLogger() {
    return window.OTA && window.OTA.logger || window.logger;
}

class MultiOrderManager {
    constructor() {
        this.datePatterns = [
            // YYYY-MM-DD格式
            /(\d{4}[-\/]\d{1,2}[-\/]\d{1,2})/g,
            // DD/MM/YYYY或DD-MM-YYYY格式
            /(\d{1,2}[-\/]\d{1,2}[-\/]\d{4})/g,
            // 中文日期格式
            /(\d{1,2}月\d{1,2}[日号])/g,
            // 相对日期
            /(今天|明天|后天|大后天)/g,
            /(下周[一二三四五六日天]|这周[一二三四五六日天])/g,
            // 英文日期格式
            /(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{1,2}/gi,
            /\d{1,2}\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)/gi
        ];
        
        this.orderSeparators = [
            /订单\s*\d+/gi,
            /order\s*\d+/gi,
            /---+/g,
            /===+/g,
            /\n\s*\n/g // 双换行
        ];
        
        this.init();
    }

    /**
     * 初始化多订单管理器
     */
    init() {
        const logger = getLogger();
        if (logger) {
            logger.log('多订单处理管理器已初始化', 'info');
        }
    }

    /**
     * 检测文本中的多个日期
     * @param {string} text - 输入文本
     * @returns {Array} 检测到的日期数组
     */
    detectMultipleDates(text) {
        if (!text || typeof text !== 'string') {
            return [];
        }

        const detectedDates = new Set();
        
        // 使用所有日期模式进行匹配
        this.datePatterns.forEach(pattern => {
            const matches = text.match(pattern);
            if (matches) {
                matches.forEach(match => {
                    const normalizedDate = this.normalizeDateString(match);
                    if (normalizedDate) {
                        detectedDates.add(normalizedDate);
                    }
                });
            }
        });

        const datesArray = Array.from(detectedDates);
        
        const logger = getLogger();
        if (logger) {
            logger.log(`检测到 ${datesArray.length} 个日期: ${datesArray.join(', ')}`, 'info');
        }
        
        return datesArray;
    }

    /**
     * 标准化日期字符串
     * @param {string} dateStr - 原始日期字符串
     * @returns {string|null} 标准化后的日期字符串 (YYYY-MM-DD)
     */
    normalizeDateString(dateStr) {
        if (!dateStr) return null;

        try {
            const today = new Date();
            
            // 处理相对日期
            if (dateStr.includes('今天')) {
                return this.formatDate(today);
            }
            
            if (dateStr.includes('明天')) {
                const tomorrow = new Date(today);
                tomorrow.setDate(today.getDate() + 1);
                return this.formatDate(tomorrow);
            }
            
            if (dateStr.includes('后天')) {
                const dayAfterTomorrow = new Date(today);
                dayAfterTomorrow.setDate(today.getDate() + 2);
                return this.formatDate(dayAfterTomorrow);
            }
            
            if (dateStr.includes('大后天')) {
                const threeDaysLater = new Date(today);
                threeDaysLater.setDate(today.getDate() + 3);
                return this.formatDate(threeDaysLater);
            }
            
            // 处理周几
            const weekDayMatch = dateStr.match(/(下周|这周)([一二三四五六日天])/);
            if (weekDayMatch) {
                const isNextWeek = weekDayMatch[1] === '下周';
                const dayName = weekDayMatch[2];
                const targetDate = this.getDateByWeekDay(dayName, isNextWeek);
                return this.formatDate(targetDate);
            }
            
            // 处理中文月日格式
            const chineseDateMatch = dateStr.match(/(\d{1,2})月(\d{1,2})[日号]/);
            if (chineseDateMatch) {
                const month = parseInt(chineseDateMatch[1]);
                const day = parseInt(chineseDateMatch[2]);
                const year = today.getFullYear();
                const date = new Date(year, month - 1, day);
                return this.formatDate(date);
            }
            
            // 处理标准日期格式
            const standardDate = new Date(dateStr);
            if (!isNaN(standardDate.getTime())) {
                return this.formatDate(standardDate);
            }
            
            return null;
            
        } catch (error) {
            const logger = getLogger();
            if (logger) {
                logger.log(`日期标准化失败: ${dateStr} - ${error.message}`, 'error');
            }
            return null;
        }
    }

    /**
     * 根据周几获取日期
     * @param {string} dayName - 周几的中文名称
     * @param {boolean} isNextWeek - 是否为下周
     * @returns {Date} 目标日期
     */
    getDateByWeekDay(dayName, isNextWeek = false) {
        const dayMap = {
            '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '日': 0, '天': 0
        };
        
        const targetDay = dayMap[dayName];
        if (targetDay === undefined) return new Date();
        
        const today = new Date();
        const currentDay = today.getDay();
        
        let daysToAdd = targetDay - currentDay;
        if (isNextWeek) {
            daysToAdd += 7;
        } else if (daysToAdd <= 0) {
            daysToAdd += 7; // 如果是这周但已经过了，则指向下周
        }
        
        const targetDate = new Date(today);
        targetDate.setDate(today.getDate() + daysToAdd);
        
        return targetDate;
    }

    /**
     * 格式化日期为YYYY-MM-DD
     * @param {Date} date - 日期对象
     * @returns {string} 格式化后的日期字符串
     */
    formatDate(date) {
        if (!date || isNaN(date.getTime())) return null;
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        
        return `${year}-${month}-${day}`;
    }

    /**
     * 检测是否为多订单模式
     * @param {string} text - 输入文本
     * @returns {boolean} 是否为多订单模式
     */
    isMultiOrderMode(text) {
        if (!text || typeof text !== 'string') {
            return false;
        }

        // 检测多个日期
        const dates = this.detectMultipleDates(text);
        if (dates.length > 1) {
            getLogger().log('检测到多个日期，启动多订单模式', 'info');
            return true;
        }

        // 检测订单分隔符
        const hasOrderSeparators = this.orderSeparators.some(pattern => {
            const matches = text.match(pattern);
            return matches && matches.length > 1;
        });

        if (hasOrderSeparators) {
            getLogger().log('检测到订单分隔符，启动多订单模式', 'info');
            return true;
        }

        // 检测多个明确的订单标识
        const orderKeywords = [
            /订单\s*[1-9]/gi,
            /order\s*[1-9]/gi,
            /第[一二三四五六七八九十]\s*个/gi,
            /第\s*[1-9]\s*个/gi
        ];

        const hasMultipleOrderKeywords = orderKeywords.some(pattern => {
            const matches = text.match(pattern);
            return matches && matches.length > 1;
        });

        if (hasMultipleOrderKeywords) {
            getLogger().log('检测到多个订单关键词，启动多订单模式', 'info');
            return true;
        }

        return false;
    }

    /**
     * 智能分割订单文本
     * @param {string} text - 输入文本
     * @returns {Array} 分割后的订单文本数组
     */
    splitOrderText(text) {
        if (!text || typeof text !== 'string') {
            return [text];
        }

        // 如果不是多订单模式，直接返回原文本
        if (!this.isMultiOrderMode(text)) {
            return [text];
        }

        let segments = [text];

        // 按订单编号分割
        const orderNumberPattern = /(?=订单\s*\d+|order\s*\d+)/gi;
        if (orderNumberPattern.test(text)) {
            segments = text.split(orderNumberPattern).filter(segment => segment.trim());
        }

        // 按分隔线分割
        if (segments.length === 1) {
            const separatorPattern = /---+|===+/g;
            if (separatorPattern.test(text)) {
                segments = text.split(separatorPattern).filter(segment => segment.trim());
            }
        }

        // 按双换行分割（如果其他方法都失败）
        if (segments.length === 1) {
            segments = text.split(/\n\s*\n/).filter(segment => segment.trim());
        }

        // 过滤掉太短的片段
        segments = segments.filter(segment => segment.trim().length > 20);

        getLogger().log(`文本已分割为 ${segments.length} 个订单片段`, 'info');

        return segments.length > 0 ? segments : [text];
    }

    /**
     * 为多订单生成参考号
     * @param {string} baseReference - 基础参考号
     * @param {number} orderCount - 订单数量
     * @returns {Array} 参考号数组
     */
    generateMultiOrderReferences(baseReference, orderCount) {
        if (!baseReference || orderCount <= 1) {
            return [baseReference];
        }

        const references = [];
        
        // 如果基础参考号已经有编号，则使用该编号作为起始
        const numberMatch = baseReference.match(/(\d+)$/);
        const baseNumber = numberMatch ? parseInt(numberMatch[1]) : 1;
        const basePrefix = numberMatch ? baseReference.replace(/\d+$/, '') : baseReference + '-';

        for (let i = 0; i < orderCount; i++) {
            const orderNumber = String(baseNumber + i).padStart(2, '0');
            references.push(`${basePrefix}${orderNumber}`);
        }

        getLogger().log(`已生成 ${orderCount} 个参考号: ${references.join(', ')}`, 'info');

        return references;
    }

    /**
     * 处理多订单数据
     * @param {Array} ordersData - 订单数据数组
     * @param {string} originalText - 原始文本
     * @returns {Array} 处理后的订单数据数组
     */
    processMultiOrderData(ordersData, originalText) {
        if (!Array.isArray(ordersData) || ordersData.length <= 1) {
            return ordersData;
        }

        // 获取基础OTA参考号
        const baseReference = ordersData[0]?.ota_reference_number || 'GMH-001';
        
        // 生成多个参考号
        const references = this.generateMultiOrderReferences(baseReference, ordersData.length);

        // 为每个订单分配参考号和序号
        const processedOrders = ordersData.map((orderData, index) => ({
            ...orderData,
            ota_reference_number: references[index],
            order_sequence: index + 1,
            total_orders: ordersData.length,
            is_multi_order: true,
            original_text_segment: this.splitOrderText(originalText)[index] || originalText
        }));

        getLogger().log(`已处理 ${processedOrders.length} 个多订单数据`, 'info');

        return processedOrders;
    }

    /**
     * 获取多订单统计信息
     * @param {Array} ordersData - 订单数据数组
     * @returns {Object} 统计信息
     */
    getMultiOrderStats(ordersData) {
        if (!Array.isArray(ordersData)) {
            return { isMultiOrder: false, orderCount: 0 };
        }

        const orderCount = ordersData.length;
        const dates = [...new Set(ordersData.map(order => order.pickup_date).filter(Boolean))];
        const serviceTypes = [...new Set(ordersData.map(order => order.sub_category_id).filter(Boolean))];
        const customers = [...new Set(ordersData.map(order => order.customer_name).filter(Boolean))];

        return {
            isMultiOrder: orderCount > 1,
            orderCount: orderCount,
            uniqueDates: dates.length,
            uniqueServiceTypes: serviceTypes.length,
            uniqueCustomers: customers.length,
            dateRange: dates.length > 1 ? `${dates[0]} 至 ${dates[dates.length - 1]}` : dates[0] || null
        };
    }
}

// 创建全局实例
let multiOrderManagerInstance = null;

/**
 * 获取多订单管理器实例
 * @returns {MultiOrderManager} 管理器实例
 */
function getMultiOrderManager() {
    if (!multiOrderManagerInstance) {
        multiOrderManagerInstance = new MultiOrderManager();
    }
    return multiOrderManagerInstance;
}

// 导出到全局作用域
window.MultiOrderManager = MultiOrderManager;
window.getMultiOrderManager = getMultiOrderManager;
